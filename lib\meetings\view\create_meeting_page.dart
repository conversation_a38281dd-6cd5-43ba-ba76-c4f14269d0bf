import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:society_management/injector/injector.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/repository/i_meeting_repository.dart';
import 'package:society_management/users/repository/i_user_repository.dart';
import 'package:society_management/utility/utility.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/common_gradient_button.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class CreateMeetingPage extends StatefulWidget {
  const CreateMeetingPage({super.key});

  @override
  State<CreateMeetingPage> createState() => _CreateMeetingPageState();
}

class _CreateMeetingPageState extends State<CreateMeetingPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _venueController = TextEditingController();

  // Add repositories
  late final IMeetingRepository _meetingRepository;
  late final IUserRepository _userRepository;

  MeetingType _selectedType = MeetingType.generalBody;
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _startTime = const TimeOfDay(hour: 10, minute: 0);
  TimeOfDay _endTime = const TimeOfDay(hour: 11, minute: 0);
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _meetingRepository = getIt<IMeetingRepository>();
    _userRepository = getIt<IUserRepository>();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _venueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: 'Schedule Meeting',
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeaderSection(),
              const SizedBox(height: 24),

              // Meeting Details
              _buildMeetingDetailsSection(),
              const SizedBox(height: 24),

              // Date and Time
              _buildDateTimeSection(),
              const SizedBox(height: 24),

              // Venue
              _buildVenueSection(),
              const SizedBox(height: 32),

              // Create Button
              _buildCreateButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF4F46E5).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.event_note,
                color: Color(0xFF4F46E5),
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Schedule New Meeting',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Create a meeting for society members',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeetingDetailsSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Meeting Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // Meeting Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Meeting Title',
                hintText: 'Enter meeting title',
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a meeting title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Meeting Type
            DropdownButtonFormField<MeetingType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Meeting Type',
                prefixIcon: Icon(Icons.category),
              ),
              items: MeetingType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_formatMeetingType(type)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedType = value);
                }
              },
            ),
            const SizedBox(height: 16),

            // Description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter meeting description',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a meeting description';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Date & Time',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // Date Picker
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Meeting Date'),
              subtitle: Text(DateFormat('EEEE, MMM dd, yyyy').format(_selectedDate)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _selectDate,
            ),
            const Divider(),

            // Start Time
            ListTile(
              leading: const Icon(Icons.access_time),
              title: const Text('Start Time'),
              subtitle: Text(_startTime.format(context)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _selectTime(true),
            ),
            const Divider(),

            // End Time
            ListTile(
              leading: const Icon(Icons.access_time_filled),
              title: const Text('End Time'),
              subtitle: Text(_endTime.format(context)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _selectTime(false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVenueSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Venue',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _venueController,
              decoration: const InputDecoration(
                labelText: 'Meeting Venue',
                hintText: 'e.g., Society Hall, Clubhouse',
                prefixIcon: Icon(Icons.location_on),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a meeting venue';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: CommonGradientButton(
        text: _isLoading ? 'Creating Meeting...' : 'Create Meeting',
        onPressed: _isLoading ? null : _createMeeting,
      ),
    );
  }

  String _formatMeetingType(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return 'General Body Meeting';
      case MeetingType.committee:
        return 'Committee Meeting';
      case MeetingType.emergency:
        return 'Emergency Meeting';
      case MeetingType.lineMeeting:
        return 'Line Meeting';
      case MeetingType.maintenance:
        return 'Maintenance Meeting';
      case MeetingType.festivalEvent:
        return 'Festival/Event Planning';
    }
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() => _selectedDate = picked);
    }
  }

  Future<void> _selectTime(bool isStartTime) async {
    final picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
    );
    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
          // Auto-adjust end time to be 1 hour after start time
          final startMinutes = _startTime.hour * 60 + _startTime.minute;
          final endMinutes = startMinutes + 60;
          _endTime = TimeOfDay(
            hour: (endMinutes ~/ 60) % 24,
            minute: endMinutes % 60,
          );
        } else {
          _endTime = picked;
        }
      });
    }
  }

  Future<void> _createMeeting() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Get current user
      final userResult = await _userRepository.getCurrentUser();
      final currentUser = userResult.fold(
        (failure) => throw Exception('Failed to get current user: ${failure.message}'),
        (user) => user,
      );

      // Create start and end DateTime objects
      final startDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final endDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      // Create organizer as first attendee
      final organizerAttendee = AttendeeInfo(
        userId: currentUser.id ?? '',
        userName: currentUser.name ?? 'Unknown',
        userEmail: currentUser.email ?? '',
        lineNumber: currentUser.lineNumber,
        role: currentUser.role ?? 'Member',
        status: AttendeeStatus.accepted, // Organizer automatically accepts
        responseDate: DateTime.now(),
      );

      // Create meeting model
      final meeting = MeetingModel(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        type: _selectedType,
        scheduledDate: _selectedDate,
        startTime: startDateTime,
        endTime: endDateTime,
        venue: _venueController.text.trim(),
        organizer: currentUser.id ?? '',
        organizerName: currentUser.name ?? 'Unknown',
        invitedUsers: [], // Legacy field for backward compatibility
        attendees: [organizerAttendee], // New Phase 2 attendee system
        lineNumbers: currentUser.lineNumber != null ? [currentUser.lineNumber!] : [],
        status: MeetingStatus.scheduled,
        agenda: [],
        actionItems: [],
        isRecurring: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        attendeeResponses: {currentUser.id ?? '': AttendeeStatus.accepted}, // Legacy field
        allowRSVP: true,
        rsvpDeadline: startDateTime.subtract(const Duration(hours: 2)), // 2 hours before meeting
        isLive: false,
        liveData: {},
      );

      // Save meeting to repository
      final result = await _meetingRepository.createMeeting(meeting);

      result.fold(
        (failure) => throw Exception('Failed to create meeting: ${failure.message}'),
        (meetingId) {
          Utility.toast(message: 'Meeting created successfully!');
          if (mounted) {
            Navigator.pop(context, true); // Return true to indicate success
          }
        },
      );
    } catch (e) {
      Utility.toast(message: 'Error creating meeting: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
