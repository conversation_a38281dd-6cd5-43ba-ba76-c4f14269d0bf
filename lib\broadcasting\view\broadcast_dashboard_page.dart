import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:society_management/broadcasting/model/broadcast_model.dart';
import 'package:society_management/broadcasting/service/broadcast_service.dart';
import 'package:society_management/broadcasting/view/create_broadcast_page.dart';
import 'package:society_management/constants/app_colors.dart';
import 'package:society_management/utility/extentions/navigation_extension.dart';
import 'package:society_management/utility/utility.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class BroadcastDashboardPage extends StatefulWidget {
  const BroadcastDashboardPage({super.key});

  @override
  State<BroadcastDashboardPage> createState() => _BroadcastDashboardPageState();
}

class _BroadcastDashboardPageState extends State<BroadcastDashboardPage> {
  final BroadcastService _broadcastService = BroadcastService(BroadcastRepository());
  List<BroadcastModel> _broadcasts = [];
  Map<String, dynamic> _analytics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load recent broadcasts
      final broadcastsResult = await BroadcastRepository().getRecentBroadcasts(limit: 20);
      broadcastsResult.fold(
        (failure) => Utility.toast(message: 'Error loading broadcasts: ${failure.message}'),
        (broadcasts) => _broadcasts = broadcasts,
      );

      // Load analytics
      _analytics = await _broadcastService.getDashboardStats();
    } catch (e) {
      Utility.toast(message: 'Error loading data: $e');
    }

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Broadcasting Center',
        showDivider: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await context.push(const CreateBroadcastPage());
          if (result == true) {
            _loadData();
          }
        },
        backgroundColor: AppColors.primaryBlue,
        icon: const Icon(Icons.campaign, color: Colors.white),
        label: const Text('New Broadcast', style: TextStyle(color: Colors.white)),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAnalyticsSection(),
                    const Gap(20),
                    _buildQuickActionsSection(),
                    const Gap(20),
                    _buildRecentBroadcastsSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildAnalyticsSection() {
    final weeklyStats = _analytics['weekly'] ?? {};
    final monthlyStats = _analytics['monthly'] ?? {};

    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: AppColors.primaryBlue),
                Gap(8),
                Text(
                  'Broadcasting Analytics',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Gap(16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'This Week',
                    '${weeklyStats['total_broadcasts'] ?? 0}',
                    'Broadcasts',
                    Icons.campaign,
                    AppColors.primaryBlue,
                  ),
                ),
                const Gap(12),
                Expanded(
                  child: _buildStatCard(
                    'This Month',
                    '${monthlyStats['total_broadcasts'] ?? 0}',
                    'Broadcasts',
                    Icons.calendar_month,
                    AppColors.primaryGreen,
                  ),
                ),
              ],
            ),
            const Gap(12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Delivery Rate',
                    '${weeklyStats['delivery_rate'] ?? '0.0'}%',
                    'This Week',
                    Icons.send,
                    AppColors.primaryOrange,
                  ),
                ),
                const Gap(12),
                Expanded(
                  child: _buildStatCard(
                    'Read Rate',
                    '${weeklyStats['read_rate'] ?? '0.0'}%',
                    'This Week',
                    Icons.mark_email_read,
                    AppColors.primaryPurple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, String subtitle, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Gap(4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const Gap(4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 10,
              color: color.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.flash_on, color: AppColors.primaryOrange),
                Gap(8),
                Text(
                  'Quick Actions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Gap(16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'Emergency Alert',
                    Icons.warning,
                    Colors.red,
                    () => _createQuickBroadcast(BroadcastType.emergency),
                  ),
                ),
                const Gap(12),
                Expanded(
                  child: _buildQuickActionButton(
                    'Announcement',
                    Icons.campaign,
                    AppColors.primaryBlue,
                    () => _createQuickBroadcast(BroadcastType.announcement),
                  ),
                ),
              ],
            ),
            const Gap(12),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    'Maintenance Notice',
                    Icons.build,
                    AppColors.primaryOrange,
                    () => _createQuickBroadcast(BroadcastType.maintenance),
                  ),
                ),
                const Gap(12),
                Expanded(
                  child: _buildQuickActionButton(
                    'Event Invitation',
                    Icons.event,
                    AppColors.primaryGreen,
                    () => _createQuickBroadcast(BroadcastType.event),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const Gap(4),
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentBroadcastsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Row(
              children: [
                Icon(Icons.history, color: AppColors.primaryBlue),
                Gap(8),
                Text(
                  'Recent Broadcasts',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            TextButton(
              onPressed: () {
                // Navigate to full broadcast history
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const Gap(12),
        if (_broadcasts.isEmpty)
          ThemeAwareCard(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(Icons.campaign_outlined, size: 48, color: Colors.grey[400]),
                    const Gap(8),
                    Text(
                      'No broadcasts yet',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const Gap(4),
                    Text(
                      'Create your first broadcast to get started',
                      style: TextStyle(color: Colors.grey[500], fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          ...(_broadcasts.take(5).map((broadcast) => _buildBroadcastCard(broadcast))),
      ],
    );
  }

  Widget _buildBroadcastCard(BroadcastModel broadcast) {
    return ThemeAwareCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getTypeColor(broadcast.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                broadcast.type.emoji,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const Gap(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    broadcast.title,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Gap(2),
                  Text(
                    broadcast.message,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Gap(4),
                  Row(
                    children: [
                      Icon(Icons.person, size: 12, color: Colors.grey[500]),
                      const Gap(2),
                      Text(
                        broadcast.creatorName,
                        style: TextStyle(fontSize: 10, color: Colors.grey[500]),
                      ),
                      const Gap(8),
                      Icon(Icons.access_time, size: 12, color: Colors.grey[500]),
                      const Gap(2),
                      Text(
                        _formatDate(broadcast.createdAt),
                        style: TextStyle(fontSize: 10, color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(broadcast.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                broadcast.status.name.toUpperCase(),
                style: TextStyle(
                  fontSize: 8,
                  color: _getStatusColor(broadcast.status),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(BroadcastType type) {
    switch (type) {
      case BroadcastType.emergency:
        return Colors.red;
      case BroadcastType.announcement:
        return AppColors.primaryBlue;
      case BroadcastType.maintenance:
        return AppColors.primaryOrange;
      case BroadcastType.event:
        return AppColors.primaryGreen;
      case BroadcastType.reminder:
        return AppColors.primaryPurple;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(BroadcastStatus status) {
    switch (status) {
      case BroadcastStatus.sent:
        return AppColors.primaryGreen;
      case BroadcastStatus.scheduled:
        return AppColors.primaryOrange;
      case BroadcastStatus.draft:
        return Colors.grey;
      case BroadcastStatus.failed:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _createQuickBroadcast(BroadcastType type) async {
    final result = await context.push(CreateBroadcastPage(initialType: type));
    if (result == true) {
      _loadData();
    }
  }
}
