import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/repository/i_meeting_repository.dart';
import 'package:society_management/utility/failure.dart';

@Injectable(as: IMeetingRepository)
class MeetingRepository implements IMeetingRepository {
  final FirebaseFirestore _firestore;
  static const String _collection = 'meetings';

  MeetingRepository(this._firestore);

  @override
  Future<Either<Failure, String>> createMeeting(MeetingModel meeting) async {
    try {
      final docRef = await _firestore.collection(_collection).add(meeting.toFirestore());
      return Right(docRef.id);
    } catch (e) {
      return Left(Failure('Failed to create meeting: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateMeeting(MeetingModel meeting) async {
    try {
      if (meeting.id == null) {
        return const Left(Failure('Meeting ID is required for update'));
      }

      await _firestore.collection(_collection).doc(meeting.id).update(meeting.toFirestore());

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to update meeting: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteMeeting(String meetingId) async {
    try {
      await _firestore.collection(_collection).doc(meetingId).delete();
      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to delete meeting: $e'));
    }
  }

  @override
  Future<Either<Failure, MeetingModel>> getMeetingById(String meetingId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(meetingId).get();

      if (!doc.exists) {
        return const Left(Failure('Meeting not found'));
      }

      return Right(MeetingModel.fromFirestore(doc));
    } catch (e) {
      return Left(Failure('Failed to get meeting: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getAllMeetings() async {
    try {
      final querySnapshot = await _firestore.collection(_collection).orderBy('scheduledDate', descending: true).get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByOrganizer(String organizerId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('organizer', isEqualTo: organizerId)
          .orderBy('scheduledDate', descending: true)
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings by organizer: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByAttendee(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('invitedUsers', arrayContains: userId)
          .orderBy('scheduledDate', descending: true)
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings by attendee: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByLine(String lineNumber) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('lineNumbers', arrayContains: lineNumber)
          .orderBy('scheduledDate', descending: true)
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings by line: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByType(MeetingType type) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('type', isEqualTo: type.toString().split('.').last)
          .orderBy('scheduledDate', descending: true)
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings by type: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByStatus(MeetingStatus status) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: status.toString().split('.').last)
          .orderBy('scheduledDate', descending: true)
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings by status: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getUpcomingMeetings() async {
    try {
      final now = DateTime.now();
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('scheduledDate', isGreaterThanOrEqualTo: Timestamp.fromDate(now))
          .where('status', whereIn: ['scheduled', 'ongoing'])
          .orderBy('scheduledDate')
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get upcoming meetings: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('scheduledDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('scheduledDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('scheduledDate')
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get meetings by date range: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateAttendeeResponse(
    String meetingId,
    String userId,
    AttendeeStatus status,
  ) async {
    try {
      // Get the current meeting
      final doc = await _firestore.collection(_collection).doc(meetingId).get();
      if (!doc.exists) {
        return Left(Failure('Meeting not found'));
      }

      final meeting = MeetingModel.fromFirestore(doc);

      // Update attendees list
      final updatedAttendees = meeting.attendees.map((attendee) {
        if (attendee.userId == userId) {
          return attendee.copyWith(
            status: status,
            responseDate: DateTime.now(),
          );
        }
        return attendee;
      }).toList();

      // Also update the legacy attendeeResponses for backward compatibility
      final updatedResponses = Map<String, AttendeeStatus>.from(meeting.attendeeResponses);
      updatedResponses[userId] = status;

      await _firestore.collection(_collection).doc(meetingId).update({
        'attendees': updatedAttendees.map((a) => a.toMap()).toList(),
        'attendeeResponses.$userId': status.toString().split('.').last,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to update attendee response: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> addMeetingMinutes(
    String meetingId,
    String minutes,
  ) async {
    try {
      await _firestore.collection(_collection).doc(meetingId).update({
        'meetingMinutes': minutes,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to add meeting minutes: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> addActionItem(
    String meetingId,
    ActionItem actionItem,
  ) async {
    try {
      final doc = await _firestore.collection(_collection).doc(meetingId).get();
      final meeting = MeetingModel.fromFirestore(doc);

      final updatedActionItems = [...meeting.actionItems, actionItem];

      await _firestore.collection(_collection).doc(meetingId).update({
        'actionItems': updatedActionItems.map((item) => item.toMap()).toList(),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to add action item: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateActionItemStatus(
    String meetingId,
    String actionItemId,
    bool isCompleted,
  ) async {
    try {
      final doc = await _firestore.collection(_collection).doc(meetingId).get();
      final meeting = MeetingModel.fromFirestore(doc);

      final updatedActionItems = meeting.actionItems.map((item) {
        if (item.id == actionItemId) {
          return ActionItem(
            id: item.id,
            title: item.title,
            description: item.description,
            assignedTo: item.assignedTo,
            assignedToName: item.assignedToName,
            dueDate: item.dueDate,
            isCompleted: isCompleted,
            createdAt: item.createdAt,
          );
        }
        return item;
      }).toList();

      await _firestore.collection(_collection).doc(meetingId).update({
        'actionItems': updatedActionItems.map((item) => item.toMap()).toList(),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to update action item status: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ActionItem>>> getActionItemsForUser(String userId) async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();

      List<ActionItem> userActionItems = [];

      for (final doc in querySnapshot.docs) {
        final meeting = MeetingModel.fromFirestore(doc);
        final userItems = meeting.actionItems.where((item) => item.assignedTo == userId).toList();
        userActionItems.addAll(userItems);
      }

      return Right(userActionItems);
    } catch (e) {
      return Left(Failure('Failed to get action items for user: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ActionItem>>> getPendingActionItems() async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();

      List<ActionItem> pendingActionItems = [];

      for (final doc in querySnapshot.docs) {
        final meeting = MeetingModel.fromFirestore(doc);
        final pendingItems = meeting.actionItems.where((item) => !item.isCompleted).toList();
        pendingActionItems.addAll(pendingItems);
      }

      return Right(pendingActionItems);
    } catch (e) {
      return Left(Failure('Failed to get pending action items: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateMeetingStatus(
    String meetingId,
    MeetingStatus status,
  ) async {
    try {
      await _firestore.collection(_collection).doc(meetingId).update({
        'status': status.toString().split('.').last,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to update meeting status: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getMeetingStatistics() async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();
      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      final stats = {
        'totalMeetings': meetings.length,
        'upcomingMeetings': meetings
            .where((m) => m.scheduledDate.isAfter(DateTime.now()) && m.status == MeetingStatus.scheduled)
            .length,
        'completedMeetings': meetings.where((m) => m.status == MeetingStatus.completed).length,
        'cancelledMeetings': meetings.where((m) => m.status == MeetingStatus.cancelled).length,
        'totalActionItems': meetings.fold<int>(0, (total, m) => total + m.actionItems.length),
        'pendingActionItems':
            meetings.fold<int>(0, (total, m) => total + m.actionItems.where((a) => !a.isCompleted).length),
      };

      return Right(stats);
    } catch (e) {
      return Left(Failure('Failed to get meeting statistics: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> searchMeetings(String query) async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();

      final meetings = querySnapshot.docs
          .map((doc) => MeetingModel.fromFirestore(doc))
          .where((meeting) =>
              meeting.title.toLowerCase().contains(query.toLowerCase()) ||
              meeting.description.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to search meetings: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MeetingModel>>> getRecurringMeetings() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isRecurring', isEqualTo: true)
          .orderBy('scheduledDate', descending: true)
          .get();

      final meetings = querySnapshot.docs.map((doc) => MeetingModel.fromFirestore(doc)).toList();

      return Right(meetings);
    } catch (e) {
      return Left(Failure('Failed to get recurring meetings: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelMeeting(String meetingId, String reason) async {
    try {
      await _firestore.collection(_collection).doc(meetingId).update({
        'status': MeetingStatus.cancelled.toString().split('.').last,
        'cancellationReason': reason,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to cancel meeting: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> postponeMeeting(
    String meetingId,
    DateTime newDate,
    DateTime newStartTime,
    DateTime newEndTime,
  ) async {
    try {
      await _firestore.collection(_collection).doc(meetingId).update({
        'status': MeetingStatus.postponed.toString().split('.').last,
        'scheduledDate': Timestamp.fromDate(newDate),
        'startTime': Timestamp.fromDate(newStartTime),
        'endTime': Timestamp.fromDate(newEndTime),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      return const Right(null);
    } catch (e) {
      return Left(Failure('Failed to postpone meeting: $e'));
    }
  }
}
