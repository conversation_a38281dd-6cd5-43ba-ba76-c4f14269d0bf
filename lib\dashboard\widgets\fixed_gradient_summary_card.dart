import 'package:flutter/material.dart';
import 'package:society_management/theme/theme_utils.dart';

class GradientSummaryCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final List<Color> gradientColors;
  final VoidCallback? onTap;
  final bool showAnimation;

  const GradientSummaryCard({
    super.key,
    required this.icon,
    required this.title,
    required this.value,
    required this.gradientColors,
    this.onTap,
    this.showAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ThemeUtils.isDarkMode(context);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradientColors,
            ),
            boxShadow: const [
              BoxShadow(
                color: Color(0x4D000000), // 30% opacity black shadow
                blurRadius: 8,
                offset: Offset(0, 4),
              ),
            ],
          ),
          height: 140, // Fixed height for all cards
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0x33FFFFFF), // 20% opacity white
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    if (onTap != null)
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Color(0x33FFFFFF), // 20% opacity white
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.arrow_forward,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                  ],
                ),
                const Spacer(),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20, // Slightly smaller font
                    overflow: TextOverflow.ellipsis, // Handle overflow
                  ),
                  maxLines: 1, // Limit to one line
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: const TextStyle(
                    color: Color(0xCCFFFFFF), // 80% opacity white
                    fontSize: 13, // Slightly smaller font
                    overflow: TextOverflow.ellipsis, // Handle overflow
                  ),
                  maxLines: 1, // Limit to one line
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
