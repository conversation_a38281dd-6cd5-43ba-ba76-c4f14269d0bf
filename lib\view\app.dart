import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:society_management/cubit/refresh_cubit.dart';
import 'package:society_management/dashboard/cubit/dashboard_cubit.dart';
import 'package:society_management/injector/injector.dart';
import 'package:society_management/splash/view/splash_page.dart';
import 'package:society_management/theme/app_theme.dart';
import 'package:society_management/theme/theme_provider.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return AppWrapper(
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp(
            title: 'KDV Management',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.light,
            darkTheme: AppTheme.dark,
            themeMode: context.read<ThemeCubit>().themeMode,
            locale: const Locale.fromSubtags(languageCode: 'en'),
            home: const SplashPage(),
          );
        },
      ),
    );
  }
}

class AppWrapper extends StatelessWidget {
  const AppWrapper({
    required this.child,
    super.key,
  });
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<RefreshCubit>(),
        ),
        BlocProvider(
          create: (context) => ThemeCubit(),
        ),
        BlocProvider(
          create: (context) => DashboardCubit(),
        ),
      ],
      child: child,
    );
  }
}
