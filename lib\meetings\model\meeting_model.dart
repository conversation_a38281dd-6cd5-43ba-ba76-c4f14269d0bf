import 'package:cloud_firestore/cloud_firestore.dart';

enum MeetingType {
  generalBody,
  committee,
  emergency,
  lineMeeting,
  maintenance,
  festivalEvent,
}

enum MeetingStatus {
  scheduled,
  ongoing,
  completed,
  cancelled,
  postponed,
}

enum AttendeeStatus {
  invited,
  accepted,
  declined,
  tentative,
  attended,
  absent,
}

enum NotificationStatus {
  pending,
  sent,
  failed,
}

class AttendeeInfo {
  final String userId;
  final String userName;
  final String userEmail;
  final String? lineNumber;
  final String role;
  final AttendeeStatus status;
  final DateTime? responseDate;
  final String? responseNote;

  AttendeeInfo({
    required this.userId,
    required this.userName,
    required this.userEmail,
    this.lineNumber,
    required this.role,
    this.status = AttendeeStatus.invited,
    this.responseDate,
    this.responseNote,
  });

  factory AttendeeInfo.fromMap(Map<String, dynamic> map) {
    return AttendeeInfo(
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userEmail: map['userEmail'] ?? '',
      lineNumber: map['lineNumber'],
      role: map['role'] ?? '',
      status: AttendeeStatus.values.firstWhere(
        (e) => e.toString() == 'AttendeeStatus.${map['status']}',
        orElse: () => AttendeeStatus.invited,
      ),
      responseDate: map['responseDate'] != null ? (map['responseDate'] as Timestamp).toDate() : null,
      responseNote: map['responseNote'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'lineNumber': lineNumber,
      'role': role,
      'status': status.toString().split('.').last,
      'responseDate': responseDate != null ? Timestamp.fromDate(responseDate!) : null,
      'responseNote': responseNote,
    };
  }

  AttendeeInfo copyWith({
    String? userId,
    String? userName,
    String? userEmail,
    String? lineNumber,
    String? role,
    AttendeeStatus? status,
    DateTime? responseDate,
    String? responseNote,
  }) {
    return AttendeeInfo(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      lineNumber: lineNumber ?? this.lineNumber,
      role: role ?? this.role,
      status: status ?? this.status,
      responseDate: responseDate ?? this.responseDate,
      responseNote: responseNote ?? this.responseNote,
    );
  }
}

class MeetingNotification {
  final String id;
  final String meetingId;
  final String userId;
  final String type; // reminder, rsvp_request, update, action_item
  final String title;
  final String message;
  final DateTime scheduledTime;
  final NotificationStatus status;
  final DateTime createdAt;
  final DateTime? sentAt;

  MeetingNotification({
    required this.id,
    required this.meetingId,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    required this.scheduledTime,
    this.status = NotificationStatus.pending,
    required this.createdAt,
    this.sentAt,
  });

  factory MeetingNotification.fromMap(Map<String, dynamic> map) {
    return MeetingNotification(
      id: map['id'] ?? '',
      meetingId: map['meetingId'] ?? '',
      userId: map['userId'] ?? '',
      type: map['type'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      scheduledTime: (map['scheduledTime'] as Timestamp).toDate(),
      status: NotificationStatus.values.firstWhere(
        (e) => e.toString() == 'NotificationStatus.${map['status']}',
        orElse: () => NotificationStatus.pending,
      ),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      sentAt: map['sentAt'] != null ? (map['sentAt'] as Timestamp).toDate() : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'meetingId': meetingId,
      'userId': userId,
      'type': type,
      'title': title,
      'message': message,
      'scheduledTime': Timestamp.fromDate(scheduledTime),
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'sentAt': sentAt != null ? Timestamp.fromDate(sentAt!) : null,
    };
  }
}

class MeetingModel {
  final String? id;
  final String title;
  final String description;
  final MeetingType type;
  final DateTime scheduledDate;
  final DateTime startTime;
  final DateTime endTime;
  final String venue;
  final String organizer; // User ID of the organizer
  final String organizerName;
  final List<String> invitedUsers; // User IDs (deprecated - use attendees)
  final List<AttendeeInfo> attendees; // Enhanced attendee information
  final List<String> lineNumbers; // For line-specific meetings
  final MeetingStatus status;
  final List<AgendaItem> agenda;
  final String? meetingMinutes;
  final List<ActionItem> actionItems;
  final bool isRecurring;
  final String? recurringPattern; // weekly, monthly, etc.
  final DateTime createdAt;
  final DateTime updatedAt;
  final int? quorumRequired;
  final List<String> attachments; // Document URLs
  final Map<String, AttendeeStatus> attendeeResponses; // Deprecated - use attendees
  final List<MeetingNotification> notifications;
  final bool allowRSVP;
  final DateTime? rsvpDeadline;
  final int? maxAttendees;
  final bool isLive; // For live meeting features
  final String? meetingLink; // Video conference link
  final Map<String, dynamic> liveData; // Real-time meeting data

  MeetingModel({
    this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.scheduledDate,
    required this.startTime,
    required this.endTime,
    required this.venue,
    required this.organizer,
    required this.organizerName,
    required this.invitedUsers,
    this.attendees = const [],
    this.lineNumbers = const [],
    this.status = MeetingStatus.scheduled,
    this.agenda = const [],
    this.meetingMinutes,
    this.actionItems = const [],
    this.isRecurring = false,
    this.recurringPattern,
    required this.createdAt,
    required this.updatedAt,
    this.quorumRequired,
    this.attachments = const [],
    this.attendeeResponses = const {},
    this.notifications = const [],
    this.allowRSVP = true,
    this.rsvpDeadline,
    this.maxAttendees,
    this.isLive = false,
    this.meetingLink,
    this.liveData = const {},
  });

  factory MeetingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return MeetingModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      type: MeetingType.values.firstWhere(
        (e) => e.toString() == 'MeetingType.${data['type']}',
        orElse: () => MeetingType.generalBody,
      ),
      scheduledDate: (data['scheduledDate'] as Timestamp).toDate(),
      startTime: (data['startTime'] as Timestamp).toDate(),
      endTime: (data['endTime'] as Timestamp).toDate(),
      venue: data['venue'] ?? '',
      organizer: data['organizer'] ?? '',
      organizerName: data['organizerName'] ?? '',
      invitedUsers: List<String>.from(data['invitedUsers'] ?? []),
      attendees: (data['attendees'] as List<dynamic>?)?.map((item) => AttendeeInfo.fromMap(item)).toList() ?? [],
      lineNumbers: List<String>.from(data['lineNumbers'] ?? []),
      status: MeetingStatus.values.firstWhere(
        (e) => e.toString() == 'MeetingStatus.${data['status']}',
        orElse: () => MeetingStatus.scheduled,
      ),
      agenda: (data['agenda'] as List<dynamic>?)?.map((item) => AgendaItem.fromMap(item)).toList() ?? [],
      meetingMinutes: data['meetingMinutes'],
      actionItems: (data['actionItems'] as List<dynamic>?)?.map((item) => ActionItem.fromMap(item)).toList() ?? [],
      isRecurring: data['isRecurring'] ?? false,
      recurringPattern: data['recurringPattern'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      quorumRequired: data['quorumRequired'],
      attachments: List<String>.from(data['attachments'] ?? []),
      attendeeResponses: Map<String, AttendeeStatus>.from(
        (data['attendeeResponses'] as Map<String, dynamic>?)?.map(
              (key, value) => MapEntry(
                key,
                AttendeeStatus.values.firstWhere(
                  (e) => e.toString() == 'AttendeeStatus.$value',
                  orElse: () => AttendeeStatus.invited,
                ),
              ),
            ) ??
            {},
      ),
      notifications:
          (data['notifications'] as List<dynamic>?)?.map((item) => MeetingNotification.fromMap(item)).toList() ?? [],
      allowRSVP: data['allowRSVP'] ?? true,
      rsvpDeadline: data['rsvpDeadline'] != null ? (data['rsvpDeadline'] as Timestamp).toDate() : null,
      maxAttendees: data['maxAttendees'],
      isLive: data['isLive'] ?? false,
      meetingLink: data['meetingLink'],
      liveData: Map<String, dynamic>.from(data['liveData'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'scheduledDate': Timestamp.fromDate(scheduledDate),
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'venue': venue,
      'organizer': organizer,
      'organizerName': organizerName,
      'invitedUsers': invitedUsers,
      'attendees': attendees.map((item) => item.toMap()).toList(),
      'lineNumbers': lineNumbers,
      'status': status.toString().split('.').last,
      'agenda': agenda.map((item) => item.toMap()).toList(),
      'meetingMinutes': meetingMinutes,
      'actionItems': actionItems.map((item) => item.toMap()).toList(),
      'isRecurring': isRecurring,
      'recurringPattern': recurringPattern,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'quorumRequired': quorumRequired,
      'attachments': attachments,
      'attendeeResponses': attendeeResponses.map(
        (key, value) => MapEntry(key, value.toString().split('.').last),
      ),
      'notifications': notifications.map((item) => item.toMap()).toList(),
      'allowRSVP': allowRSVP,
      'rsvpDeadline': rsvpDeadline != null ? Timestamp.fromDate(rsvpDeadline!) : null,
      'maxAttendees': maxAttendees,
      'isLive': isLive,
      'meetingLink': meetingLink,
      'liveData': liveData,
    };
  }

  MeetingModel copyWith({
    String? id,
    String? title,
    String? description,
    MeetingType? type,
    DateTime? scheduledDate,
    DateTime? startTime,
    DateTime? endTime,
    String? venue,
    String? organizer,
    String? organizerName,
    List<String>? invitedUsers,
    List<AttendeeInfo>? attendees,
    List<String>? lineNumbers,
    MeetingStatus? status,
    List<AgendaItem>? agenda,
    String? meetingMinutes,
    List<ActionItem>? actionItems,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? quorumRequired,
    List<String>? attachments,
    Map<String, AttendeeStatus>? attendeeResponses,
    List<MeetingNotification>? notifications,
    bool? allowRSVP,
    DateTime? rsvpDeadline,
    int? maxAttendees,
    bool? isLive,
    String? meetingLink,
    Map<String, dynamic>? liveData,
  }) {
    return MeetingModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      venue: venue ?? this.venue,
      organizer: organizer ?? this.organizer,
      organizerName: organizerName ?? this.organizerName,
      invitedUsers: invitedUsers ?? this.invitedUsers,
      attendees: attendees ?? this.attendees,
      lineNumbers: lineNumbers ?? this.lineNumbers,
      status: status ?? this.status,
      agenda: agenda ?? this.agenda,
      meetingMinutes: meetingMinutes ?? this.meetingMinutes,
      actionItems: actionItems ?? this.actionItems,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      quorumRequired: quorumRequired ?? this.quorumRequired,
      attachments: attachments ?? this.attachments,
      attendeeResponses: attendeeResponses ?? this.attendeeResponses,
      notifications: notifications ?? this.notifications,
      allowRSVP: allowRSVP ?? this.allowRSVP,
      rsvpDeadline: rsvpDeadline ?? this.rsvpDeadline,
      maxAttendees: maxAttendees ?? this.maxAttendees,
      isLive: isLive ?? this.isLive,
      meetingLink: meetingLink ?? this.meetingLink,
      liveData: liveData ?? this.liveData,
    );
  }
}

class AgendaItem {
  final String id;
  final String title;
  final String description;
  final int estimatedDuration; // in minutes
  final String presenter;
  final int order;

  AgendaItem({
    required this.id,
    required this.title,
    required this.description,
    required this.estimatedDuration,
    required this.presenter,
    required this.order,
  });

  factory AgendaItem.fromMap(Map<String, dynamic> map) {
    return AgendaItem(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      estimatedDuration: map['estimatedDuration'] ?? 0,
      presenter: map['presenter'] ?? '',
      order: map['order'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'estimatedDuration': estimatedDuration,
      'presenter': presenter,
      'order': order,
    };
  }
}

class ActionItem {
  final String id;
  final String title;
  final String description;
  final String assignedTo; // User ID
  final String assignedToName;
  final DateTime dueDate;
  final bool isCompleted;
  final DateTime createdAt;

  ActionItem({
    required this.id,
    required this.title,
    required this.description,
    required this.assignedTo,
    required this.assignedToName,
    required this.dueDate,
    this.isCompleted = false,
    required this.createdAt,
  });

  factory ActionItem.fromMap(Map<String, dynamic> map) {
    return ActionItem(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      assignedTo: map['assignedTo'] ?? '',
      assignedToName: map['assignedToName'] ?? '',
      dueDate: (map['dueDate'] as Timestamp).toDate(),
      isCompleted: map['isCompleted'] ?? false,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'assignedTo': assignedTo,
      'assignedToName': assignedToName,
      'dueDate': Timestamp.fromDate(dueDate),
      'isCompleted': isCompleted,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}
