import 'package:society_management/dashboard/model/dashboard_stats_model.dart';
import 'package:society_management/dashboard/repository/i_dashboard_stats_repository.dart';
import 'package:society_management/dashboard/service/dashboard_stats_service.dart';
import 'package:society_management/extentions/firestore_extentions.dart';
import 'package:society_management/utility/app_typednfs.dart';
import 'package:society_management/utility/result.dart';

class DashboardStatsRepository extends IDashboardStatsRepository {
  final DashboardStatsService _service = DashboardStatsService();
  DashboardStatsRepository(super.firestore);

  @override
  FirebaseResult<void> decrementTotalExpenses(double amount) {
    // TODO: implement decrementTotalExpenses
    throw UnimplementedError();
  }

  // ========================================
  // ADMIN DASHBOARD STATS (Clean & Simple)
  // ========================================

  @override
  FirebaseResult<DashboardStatsModel> getAdminDashboardStats() async {
    return Result<DashboardStatsModel>().tryCatch(
      run: () async {
        // Get admin dashboard stats from the admin_dashboard_stats collection
        final adminStatsDoc = await firestore.adminDashboardStats.doc('stats').get();

        if (adminStatsDoc.exists) {
          final data = adminStatsDoc.data() as Map<String, dynamic>;
          return DashboardStatsModel.fromMap(data);
        } else {
          // Return default stats if no data exists
          return const DashboardStatsModel(
            totalExpenses: 0.0,
            totalMembers: 0,
            maintenanceCollected: 0.0,
            maintenancePending: 0.0,
            activeMaintenance: 0,
            fullyPaidUsers: 0,
          );
        }
      },
    );
  }
  // ========================================
  // LINE HEAD DASHBOARD STATS (Clean & Simple)
  // ========================================

  @override
  FirebaseResult<DashboardStatsModel> getLineHeadDashboardStats(String lineNumber) async {
    return Result<DashboardStatsModel>().tryCatch(
      run: () async {
        // Get line head dashboard stats
        final lineStatsDoc = await firestore.lineHeadDashboardStats.doc(lineNumber).get();

        if (lineStatsDoc.exists) {
          final data = lineStatsDoc.data() as Map<String, dynamic>;
          return DashboardStatsModel.fromMap(data);
        } else {
          // Return default stats if no data exists
          return const DashboardStatsModel(
            totalExpenses: 0.0,
            totalMembers: 0,
            maintenanceCollected: 0.0,
            maintenancePending: 0.0,
            activeMaintenance: 0,
            fullyPaidUsers: 0,
          );
        }
      },
    );
  }

  // ========================================
  // USER DASHBOARD STATS (Clean & Simple)
  // ========================================

  @override
  FirebaseResult<DashboardStatsModel> getUserDashboardStats(String userId) async {
    return Result<DashboardStatsModel>().tryCatch(
      run: () async {
        // Get user dashboard stats
        final userStatsDoc = await firestore.userSpecificStats.doc(userId).get();

        if (userStatsDoc.exists) {
          final data = userStatsDoc.data() as Map<String, dynamic>;
          return DashboardStatsModel.fromMap(data);
        } else {
          // Return default stats if no data exists
          return const DashboardStatsModel(
            totalExpenses: 0.0,
            totalMembers: 1, // User sees themselves
            maintenanceCollected: 0.0,
            maintenancePending: 0.0,
            activeMaintenance: 0,
            fullyPaidUsers: 0,
          );
        }
      },
    );
  }

  @override
  FirebaseResult<void> incrementTotalExpenses(double amount) async {
    return Result<void>().tryCatch(
      run: () async {
        await _service.addExpense(amount);
      },
    );
  }

  @override
  FirebaseResult<void> incrementTotalMembers() async {
    return Result<void>().tryCatch(
      run: () async {
        await _service.addUser();
      },
    );
  }

  @override
  Future<void> updateAdminDashboardStats() {
    // TODO: implement updateAdminDashboardStats
    throw UnimplementedError();
  }
    @override
  Future<Result<void>> updateAdminDashboard(DashboardStatsModel adminStats) async {
    return Result<void>().tryCatch(
      run: () async {
        final now = Timestamp.now();

        await firestore.adminDashboardStats.doc('stats').set({
          'total_members': adminStats.totalMembers,
          'total_expenses': adminStats.totalExpenses,
          'maintenance_collected': adminStats.maintenanceCollected,
          'maintenance_pending': adminStats.maintenancePending,
          'active_maintenance': adminStats.activeMaintenance,
          'fully_paid': adminStats.fullyPaid,
          'updated_at': now,
        });
      },
    );
  }

  @override
  Future<void> updateDashboardsForMaintenancePayment(
      {required String lineNumber,
      required double amountPaid,
      required double amountPending,
      bool isFullyPaid = false,
      String? userId}) {
    // TODO: implement updateDashboardsForMaintenancePayment
    throw UnimplementedError();
  }

  @override
  Future<void> updateDashboardsForMaintenancePeriodCreation() {
    // TODO: implement updateDashboardsForMaintenancePeriodCreation
    throw UnimplementedError();
  }

  @override
  FirebaseResult<void> updateExpenseAmount({required double oldAmount, required double newAmount}) {
    // TODO: implement updateExpenseAmount
    throw UnimplementedError();
  }

  @override
  Future<void> updateLineStats(String lineNumber) {
    // TODO: implement updateLineStats
    throw UnimplementedError();
  }
}
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:injectable/injectable.dart';
// import 'package:society_management/dashboard/model/dashboard_stats_model.dart';
// import 'package:society_management/dashboard/repository/i_dashboard_stats_repository.dart';
// import 'package:society_management/dashboard/service/dashboard_stats_service.dart';
// import 'package:society_management/extentions/firestore_extentions.dart';
// import 'package:society_management/utility/app_typednfs.dart';
// import 'package:society_management/utility/result.dart';

// /// Clean, simplified Dashboard Stats Repository
// /// Uses the DashboardStatsService for actual operations
// /// This is much cleaner and easier to maintain than the old complex repository
// @Injectable(as: IDashboardStatsRepository)
// class DashboardStatsRepository extends IDashboardStatsRepository {
//   final DashboardStatsService _service = DashboardStatsService();

//   DashboardStatsRepository(super.firestore);

//   // ========================================
//   // EXPENSE OPERATIONS (Clean & Simple)
//   // ========================================

//   @override
//   Future<Result<void>> incrementTotalExpenses(double amount) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.addExpense(amount);
//       },
//     );
//   }

//   @override
//   Future<Result<void>> decrementTotalExpenses(double amount) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.removeExpense(amount);
//       },
//     );
//   }

//   @override
//   Future<Result<void>> updateTotalExpenses({
//     required double oldAmount,
//     required double newAmount,
//   }) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.updateExpense(
//           oldAmount: oldAmount,
//           newAmount: newAmount,
//         );
//       },
//     );
//   }

//   // ========================================
//   // USER OPERATIONS (Clean & Simple)
//   // ========================================

//   @override
//   Future<Result<void>> incrementTotalMembers() async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.addUser();
//       },
//     );
//   }

//   @override
//   Future<Result<void>> decrementTotalMembers() async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.removeUser();
//       },
//     );
//   }

//   // ========================================
//   // MAINTENANCE OPERATIONS (Clean & Simple)
//   // ========================================

//   @override
//   Future<Result<void>> updateMaintenanceStats({
//     double? collectedChange,
//     double? pendingChange,
//     int? fullyPaidChange,
//   }) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.updateMaintenance(
//           collectedChange: collectedChange,
//           pendingChange: pendingChange,
//           fullyPaidChange: fullyPaidChange,
//         );
//       },
//     );
//   }

//   // ========================================
//   // LINE OPERATIONS (Clean & Simple)
//   // ========================================

//   @override
//   Future<Result<void>> updateLineHeadDashboard(String lineNumber, DashboardStatsModel lineStats) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         final now = Timestamp.now();

//         // Update line head dashboard
//         await firestore.lineHeadDashboardStats.doc(lineNumber).set({
//           'total_members': lineStats.totalMembers,
//           'total_expenses': lineStats.totalExpenses,
//           'maintenance_collected': lineStats.maintenanceCollected,
//           'maintenance_pending': lineStats.maintenancePending,
//           'active_maintenance': lineStats.activeMaintenance,
//           'fully_paid': lineStats.fullyPaid,
//           'updated_at': now,
//         });

//         // Update user dashboard for this line
//         await firestore.userSpecificStats.doc(lineNumber).set({
//           'total_members': lineStats.totalMembers,
//           'total_expenses': lineStats.totalExpenses,
//           'maintenance_collected': lineStats.maintenanceCollected,
//           'maintenance_pending': lineStats.maintenancePending,
//           'active_maintenance': lineStats.activeMaintenance,
//           'fully_paid': lineStats.fullyPaid,
//           'updated_at': now,
//         });
//       },
//     );
//   }

//   // ========================================
//   // UTILITY OPERATIONS (Clean & Simple)
//   // ========================================

//   @override
//   Future<Result<void>> refreshAllStats() async {
//     return Result<void>().tryCatch(
//       run: () async {
//         await _service.recalculateAllStats();
//       },
//     );
//   }

//   // ========================================
//   // LEGACY METHODS (For Compatibility)
//   // ========================================

//   @override
//   Future<Result<void>> updateUserDashboard(String userId, DashboardStatsModel userStats) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         final now = Timestamp.now();

//         await firestore.userSpecificStats.doc(userId).set({
//           'total_members': userStats.totalMembers,
//           'total_expenses': userStats.totalExpenses,
//           'maintenance_collected': userStats.maintenanceCollected,
//           'maintenance_pending': userStats.maintenancePending,
//           'active_maintenance': userStats.activeMaintenance,
//           'fully_paid': userStats.fullyPaid,
//           'updated_at': now,
//         });
//       },
//     );
//   }

//   @override
//   Future<Result<void>> updateAdminDashboard(DashboardStatsModel adminStats) async {
//     return Result<void>().tryCatch(
//       run: () async {
//         final now = Timestamp.now();

//         await firestore.adminDashboardStats.doc('stats').set({
//           'total_members': adminStats.totalMembers,
//           'total_expenses': adminStats.totalExpenses,
//           'maintenance_collected': adminStats.maintenanceCollected,
//           'maintenance_pending': adminStats.maintenancePending,
//           'active_maintenance': adminStats.activeMaintenance,
//           'fully_paid': adminStats.fullyPaid,
//           'updated_at': now,
//         });
//       },
//     );
//   }

//   @override
//   FirebaseResult<DashboardStatsModel> getUserStats(String lineNumber) {
//     // TODO: implement getUserStats
//     throw UnimplementedError();
//   }

//   @override
//   Future<void> updateAdminDashboardStats() {
//     // TODO: implement updateAdminDashboardStats
//     throw UnimplementedError();
//   }

//   @override
//   Future<void> updateDashboardsForMaintenancePayment(
//       {required String lineNumber,
//       required double amountPaid,
//       required double amountPending,
//       bool isFullyPaid = false,
//       String? userId}) {
//     // TODO: implement updateDashboardsForMaintenancePayment
//     throw UnimplementedError();
//   }

//   @override
//   Future<void> updateDashboardsForMaintenancePeriodCreation() {
//     // TODO: implement updateDashboardsForMaintenancePeriodCreation
//     throw UnimplementedError();
//   }

//   @override
//   FirebaseResult<void> updateExpenseAmount({required double oldAmount, required double newAmount}) {
//     // TODO: implement updateExpenseAmount
//     throw UnimplementedError();
//   }

//   @override
//   Future<void> updateLineStats(String lineNumber) {
//     // TODO: implement updateLineStats
//     throw UnimplementedError();
//   }
// }
