class AppConstants {
  // Project Leader Type
  static const String leaderUser = 'LEADER_USER';
  static const String onlyLeader = 'ONLY_LEADER';
  static const String doctor = 'DOCTOR';
  static const String firstYear = 'FIRST_YEAR';
  static const String secondYear = 'SECOND_YEAR';
  static const String thirdYear = 'THIRD_YEAR';
  static const String fourthYear = 'FOURTH_YEAR';
  static const String admin = 'ADMIN';
  static const String firstLine = 'FIRST_LINE';
  static const String secondLine = 'SECOND_LINE';
  static const String thirdLine = 'THIRD_LINE';
  static const String fourthLine = 'FOURTH_LINE';
  static const String fifthLine = 'FIFTH_LINE';
  static const String admins = 'Admin';

  static const String sunday = 'SUNDAY';
  static const String monday = 'MONDAY';
  static const String tuesday = 'TUESDAY';
  static const String wednesday = 'WEDNESDAY';
  static const String thursday = 'THURSDAY';
  static const String friday = 'FRIDAY';
  static const String saturday = 'SATURDAY';

  // ________Holiday Type_________
  static const String holiday = 'HOLIDAY';
  static const String otherHoliday = 'OTHER_HOLIDAY';
  static const String prohibitedDay = 'PROHIBITED_DAY';

  static const String planningDetails = 'Planning';
  static const String basicDetails = 'Basic Details';
  static const String shiftSetting = 'Shift Setting';
  static const String holidays = 'Holidays';
  static const String usersAndSettings = 'Users & Settings';
  static const String leaderAndUser = 'Leader + User';
  static const String leaderOnly = 'Leader Only';
  static const String doctors = 'Doctor';
  static const String firstYearResident = 'First year resident';
  static const String lineLead = 'LINE_HEAD';
  static const String lineMember = 'LINE_MEMBER';
  static const String lineHeadAndMember = 'LINE_HEAD_MEMBER';
  static const String fourthYearResident = 'Fourth year resident';
  static const String localHolidays = 'Local holidays';
  static const String otherHolidays = 'Other holidays';
  static const String holiWeekAndChristmas = 'Holi week & christmas';
  static const String psmarCompensatory = 'Psmar compensatory';
  static const String prohibitedDays = 'Prohibited days';
  // static const String doctors = 'Doctors';
  static const String availability = 'Availability';
  static const String details = 'Details';
  static const String accepted = 'Accepted';
  static const String pending = 'Pending';
  static const String rejected = 'Rejected';
  static const String personalDays = 'Personal Days ';
  static const String conferences = 'Conferences';
  static const String vactions = 'Vactions';
  static const String otherShift = 'Other Shifts';
  static const String notAvailable = 'Not Available';
  static const String notes =
      'Make sure that you have sufficient users for each Role.\n\nUse this formula:\nD  = MR * (B+1)\n\nMR: minimum user required per shift\nB : Avoid shift days\nD : number of users required for that role';

  // Helper method to get line constant from line number
  static String getLineConstant(int lineNumber) {
    switch (lineNumber) {
      case 1:
        return firstLine;
      case 2:
        return secondLine;
      case 3:
        return thirdLine;
      case 4:
        return fourthLine;
      case 5:
        return fifthLine;
      default:
        return firstLine;
    }
  }
}
