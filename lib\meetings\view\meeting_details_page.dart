import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/view/meeting_rsvp_page.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class MeetingDetailsPage extends StatefulWidget {
  final MeetingModel meeting;

  const MeetingDetailsPage({
    super.key,
    required this.meeting,
  });

  @override
  State<MeetingDetailsPage> createState() => _MeetingDetailsPageState();
}

class _MeetingDetailsPageState extends State<MeetingDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: 'Meeting Details',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeaderSection(),
            const SizedBox(height: 24),

            // Meeting Info
            _buildMeetingInfoSection(),
            const SizedBox(height: 24),

            // Date & Time
            _buildDateTimeSection(),
            const SizedBox(height: 24),

            // Agenda Section
            if (widget.meeting.agenda.isNotEmpty) ...[
              _buildAgendaSection(),
              const SizedBox(height: 24),
            ],

            // Action Items
            if (widget.meeting.actionItems.isNotEmpty) ...[
              _buildActionItemsSection(),
              const SizedBox(height: 24),
            ],

            // Meeting Minutes
            if (widget.meeting.meetingMinutes != null) ...[
              _buildMinutesSection(),
              const SizedBox(height: 24),
            ],

            // RSVP Section
            if (widget.meeting.status == MeetingStatus.scheduled) ...[
              _buildRSVPSection(),
              const SizedBox(height: 24),
            ],

            // Attendee List
            if (widget.meeting.attendees.isNotEmpty) ...[
              _buildAttendeeSection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getMeetingTypeColor(widget.meeting.type).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getMeetingTypeIcon(widget.meeting.type),
                    color: _getMeetingTypeColor(widget.meeting.type),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.meeting.title,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatMeetingType(widget.meeting.type),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: _getMeetingTypeColor(widget.meeting.type),
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(widget.meeting.status),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _formatStatus(widget.meeting.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              widget.meeting.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeetingInfoSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Meeting Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.person, 'Organizer', widget.meeting.organizerName),
            _buildInfoRow(Icons.location_on, 'Venue', widget.meeting.venue),
            _buildInfoRow(Icons.group, 'Attendees', '${widget.meeting.invitedUsers.length} invited'),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Date & Time',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.calendar_today,
              'Date',
              DateFormat('EEEE, MMM dd, yyyy').format(widget.meeting.scheduledDate),
            ),
            _buildInfoRow(
              Icons.access_time,
              'Start Time',
              DateFormat('hh:mm a').format(widget.meeting.startTime),
            ),
            _buildInfoRow(
              Icons.access_time_filled,
              'End Time',
              DateFormat('hh:mm a').format(widget.meeting.endTime),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgendaSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Meeting Agenda',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...widget.meeting.agenda.map((item) => _buildAgendaItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildActionItemsSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Action Items',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...widget.meeting.actionItems.map((item) => _buildActionItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildMinutesSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Meeting Minutes',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              widget.meeting.meetingMinutes!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
          const SizedBox(width: 12),
          Text(
            '$label:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgendaItem(AgendaItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: const Color(0xFF4F46E5).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                '${item.order}',
                style: const TextStyle(
                  color: Color(0xFF4F46E5),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                if (item.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(ActionItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            item.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
            color: item.isCompleted ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        decoration: item.isCompleted ? TextDecoration.lineThrough : null,
                      ),
                ),
                Text(
                  'Assigned to: ${item.assignedToName}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                ),
                Text(
                  'Due: ${DateFormat('MMM dd, yyyy').format(item.dueDate)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRSVPSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.how_to_vote,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'RSVP to this Meeting',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Please respond to help us plan better for this meeting.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _navigateToRSVP(),
                icon: const Icon(Icons.reply),
                label: const Text('Respond to Invitation'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendeeSection() {
    final accepted = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.accepted).length;
    final declined = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.declined).length;
    final tentative = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.tentative).length;
    final pending = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.invited).length;

    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attendee Responses',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // Response Statistics
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildStatChip('Accepted', accepted, Colors.green),
                _buildStatChip('Declined', declined, Colors.red),
                _buildStatChip('Tentative', tentative, Colors.orange),
                _buildStatChip('Pending', pending, Colors.grey),
              ],
            ),

            const SizedBox(height: 16),

            // Attendee List
            if (widget.meeting.attendees.isNotEmpty) ...[
              ...widget.meeting.attendees.take(5).map((attendee) => _buildAttendeeItem(attendee)),
              if (widget.meeting.attendees.length > 5) ...[
                const SizedBox(height: 8),
                Center(
                  child: TextButton(
                    onPressed: () => _navigateToRSVP(),
                    child: Text('View All ${widget.meeting.attendees.length} Attendees'),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $count',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildAttendeeItem(AttendeeInfo attendee) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: _getAttendeeStatusColor(attendee.status).withOpacity(0.1),
            child: Icon(
              _getAttendeeStatusIcon(attendee.status),
              color: _getAttendeeStatusColor(attendee.status),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attendee.userName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Text(
                  '${attendee.role}${attendee.lineNumber != null ? ' - Line ${attendee.lineNumber}' : ''}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getAttendeeStatusColor(attendee.status),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _formatAttendeeStatus(attendee.status),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToRSVP() async {
    try {
      // Navigate to RSVP page
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MeetingRSVPPage(meeting: widget.meeting),
        ),
      );

      // If RSVP was updated, refresh the page
      if (result == true && mounted) {
        setState(() {
          // This will trigger a rebuild to show updated attendee info
        });
      }
    } catch (e) {
      // Fallback to dialog if navigation fails
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('RSVP Feature'),
            content: Text('Unable to open RSVP page: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  // Helper methods for colors and formatting
  Color _getMeetingTypeColor(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return const Color(0xFF3B82F6);
      case MeetingType.committee:
        return const Color(0xFF10B981);
      case MeetingType.emergency:
        return const Color(0xFFEF4444);
      case MeetingType.lineMeeting:
        return const Color(0xFF8B5CF6);
      case MeetingType.maintenance:
        return const Color(0xFFF59E0B);
      case MeetingType.festivalEvent:
        return const Color(0xFFEC4899);
    }
  }

  IconData _getMeetingTypeIcon(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return Icons.groups;
      case MeetingType.committee:
        return Icons.business;
      case MeetingType.emergency:
        return Icons.warning;
      case MeetingType.lineMeeting:
        return Icons.home_work;
      case MeetingType.maintenance:
        return Icons.build;
      case MeetingType.festivalEvent:
        return Icons.celebration;
    }
  }

  String _formatMeetingType(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return 'General Body Meeting';
      case MeetingType.committee:
        return 'Committee Meeting';
      case MeetingType.emergency:
        return 'Emergency Meeting';
      case MeetingType.lineMeeting:
        return 'Line Meeting';
      case MeetingType.maintenance:
        return 'Maintenance Meeting';
      case MeetingType.festivalEvent:
        return 'Festival/Event Planning';
    }
  }

  Color _getStatusColor(MeetingStatus status) {
    switch (status) {
      case MeetingStatus.scheduled:
        return const Color(0xFF3B82F6);
      case MeetingStatus.ongoing:
        return const Color(0xFF10B981);
      case MeetingStatus.completed:
        return const Color(0xFF8B5CF6);
      case MeetingStatus.cancelled:
        return const Color(0xFFEF4444);
      case MeetingStatus.postponed:
        return const Color(0xFFF59E0B);
    }
  }

  String _formatStatus(MeetingStatus status) {
    switch (status) {
      case MeetingStatus.scheduled:
        return 'Scheduled';
      case MeetingStatus.ongoing:
        return 'Ongoing';
      case MeetingStatus.completed:
        return 'Completed';
      case MeetingStatus.cancelled:
        return 'Cancelled';
      case MeetingStatus.postponed:
        return 'Postponed';
    }
  }

  Color _getAttendeeStatusColor(AttendeeStatus status) {
    switch (status) {
      case AttendeeStatus.accepted:
        return Colors.green;
      case AttendeeStatus.declined:
        return Colors.red;
      case AttendeeStatus.tentative:
        return Colors.orange;
      case AttendeeStatus.attended:
        return Colors.blue;
      case AttendeeStatus.absent:
        return Colors.grey;
      case AttendeeStatus.invited:
        return Colors.grey;
    }
  }

  IconData _getAttendeeStatusIcon(AttendeeStatus status) {
    switch (status) {
      case AttendeeStatus.accepted:
        return Icons.check_circle;
      case AttendeeStatus.declined:
        return Icons.cancel;
      case AttendeeStatus.tentative:
        return Icons.help;
      case AttendeeStatus.attended:
        return Icons.person;
      case AttendeeStatus.absent:
        return Icons.person_off;
      case AttendeeStatus.invited:
        return Icons.mail;
    }
  }

  String _formatAttendeeStatus(AttendeeStatus status) {
    switch (status) {
      case AttendeeStatus.accepted:
        return 'Accepted';
      case AttendeeStatus.declined:
        return 'Declined';
      case AttendeeStatus.tentative:
        return 'Tentative';
      case AttendeeStatus.attended:
        return 'Attended';
      case AttendeeStatus.absent:
        return 'Absent';
      case AttendeeStatus.invited:
        return 'Invited';
    }
  }
}
