import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:society_management/injector/injector.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/repository/i_meeting_repository.dart';
import 'package:society_management/users/repository/i_user_repository.dart';
import 'package:society_management/utility/utility.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/common_gradient_button.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class MeetingRSVPPage extends StatefulWidget {
  final MeetingModel meeting;

  const MeetingRSVPPage({
    super.key,
    required this.meeting,
  });

  @override
  State<MeetingRSVPPage> createState() => _MeetingRSVPPageState();
}

class _MeetingRSVPPageState extends State<MeetingRSVPPage> {
  final IMeetingRepository _meetingRepository = getIt<IMeetingRepository>();
  final IUserRepository _userRepository = getIt<IUserRepository>();

  bool _isLoading = false;
  AttendeeStatus? _currentUserStatus;
  String? _currentUserId;
  final _noteController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCurrentUserStatus();
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentUserStatus() async {
    try {
      final userResult = await _userRepository.getCurrentUser();
      userResult.fold(
        (failure) => Utility.toast(message: 'Error loading user: ${failure.message}'),
        (user) {
          _currentUserId = user.id;
          // Find current user's status in attendees list
          final attendee = widget.meeting.attendees.firstWhere(
            (a) => a.userId == user.id,
            orElse: () => AttendeeInfo(
              userId: user.id ?? '',
              userName: user.name ?? '',
              userEmail: user.email ?? '',
              role: user.role ?? '',
              status: AttendeeStatus.invited,
            ),
          );
          setState(() {
            _currentUserStatus = attendee.status;
            _noteController.text = attendee.responseNote ?? '';
          });
        },
      );
    } catch (e) {
      Utility.toast(message: 'Error loading user status: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: 'Meeting RSVP',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Meeting Info Card
            _buildMeetingInfoCard(),
            const SizedBox(height: 24),

            // RSVP Status Card
            _buildRSVPStatusCard(),
            const SizedBox(height: 24),

            // Response Options
            _buildResponseOptionsCard(),
            const SizedBox(height: 24),

            // Note Section
            _buildNoteSection(),
            const SizedBox(height: 24),

            // Attendee List
            _buildAttendeeListCard(),
            const SizedBox(height: 32),

            // Submit Button
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildMeetingInfoCard() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getMeetingTypeColor(widget.meeting.type).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getMeetingTypeIcon(widget.meeting.type),
                    color: _getMeetingTypeColor(widget.meeting.type),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.meeting.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatMeetingType(widget.meeting.type),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: _getMeetingTypeColor(widget.meeting.type),
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
                Icons.calendar_today, 'Date', DateFormat('EEEE, MMM dd, yyyy').format(widget.meeting.scheduledDate)),
            _buildInfoRow(Icons.access_time, 'Time',
                '${DateFormat('hh:mm a').format(widget.meeting.startTime)} - ${DateFormat('hh:mm a').format(widget.meeting.endTime)}'),
            _buildInfoRow(Icons.location_on, 'Venue', widget.meeting.venue),
            _buildInfoRow(Icons.person, 'Organizer', widget.meeting.organizerName),
          ],
        ),
      ),
    );
  }

  Widget _buildRSVPStatusCard() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'RSVP Status',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            if (_currentUserStatus != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _getStatusColor(_currentUserStatus!).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getStatusColor(_currentUserStatus!),
                    width: 2,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getStatusIcon(_currentUserStatus!),
                      color: _getStatusColor(_currentUserStatus!),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Your Response: ${_formatStatus(_currentUserStatus!)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: _getStatusColor(_currentUserStatus!),
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.help_outline, color: Colors.grey),
                    const SizedBox(width: 12),
                    Text(
                      'Please respond to this meeting invitation',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
            ],
            if (widget.meeting.rsvpDeadline != null) ...[
              const SizedBox(height: 12),
              Text(
                'RSVP Deadline: ${DateFormat('MMM dd, yyyy hh:mm a').format(widget.meeting.rsvpDeadline!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResponseOptionsCard() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Will you attend this meeting?',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 20),

            // Simple Yes/No buttons
            Row(
              children: [
                Expanded(
                  child: _buildSimpleResponseButton(
                      AttendeeStatus.accepted, 'Yes, I\'ll Come', Icons.thumb_up, Colors.green),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSimpleResponseButton(
                      AttendeeStatus.declined, 'No, Can\'t Come', Icons.thumb_down, Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleResponseButton(AttendeeStatus status, String text, IconData icon, Color color) {
    final isSelected = _currentUserStatus == status;

    return GestureDetector(
      onTap: () => setState(() => _currentUserStatus = status),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              text,
              style: TextStyle(
                color: isSelected ? Colors.white : color,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Note (Optional)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _noteController,
              decoration: const InputDecoration(
                hintText: 'Add any additional comments or notes...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendeeListCard() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attendee Responses',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            _buildAttendeeStats(),
            const SizedBox(height: 16),
            if (widget.meeting.attendees.isNotEmpty) ...[
              ...widget.meeting.attendees.map((attendee) => _buildAttendeeItem(attendee)),
            ] else ...[
              Center(
                child: Text(
                  'No attendees added yet',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAttendeeStats() {
    final coming = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.accepted).length;
    final notComing = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.declined).length;
    final pending = widget.meeting.attendees.where((a) => a.status == AttendeeStatus.invited).length;

    return Row(
      children: [
        Expanded(child: _buildStatChip('Coming', coming, Colors.green)),
        const SizedBox(width: 8),
        Expanded(child: _buildStatChip('Not Coming', notComing, Colors.red)),
        const SizedBox(width: 8),
        Expanded(child: _buildStatChip('No Response', pending, Colors.grey)),
      ],
    );
  }

  Widget _buildStatChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $count',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildAttendeeItem(AttendeeInfo attendee) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: _getStatusColor(attendee.status).withOpacity(0.1),
            child: Icon(
              _getStatusIcon(attendee.status),
              color: _getStatusColor(attendee.status),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attendee.userName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Text(
                  '${attendee.role}${attendee.lineNumber != null ? ' - Line ${attendee.lineNumber}' : ''}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(attendee.status),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _formatStatus(attendee.status),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: CommonGradientButton(
        text: _isLoading ? 'Saving...' : 'Save My Response',
        onPressed: _isLoading || _currentUserStatus == null ? null : _submitResponse,
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitResponse() async {
    if (_currentUserStatus == null || _currentUserId == null) return;

    setState(() => _isLoading = true);

    try {
      final result = await _meetingRepository.updateAttendeeResponse(
        widget.meeting.id!,
        _currentUserId!,
        _currentUserStatus!,
      );

      result.fold(
        (failure) => throw Exception('Failed to update RSVP: ${failure.message}'),
        (_) {
          Utility.toast(message: 'RSVP response updated successfully!');
          Navigator.pop(context, true);
        },
      );
    } catch (e) {
      Utility.toast(message: 'Error updating RSVP: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Helper methods for colors and formatting
  Color _getMeetingTypeColor(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return const Color(0xFF3B82F6);
      case MeetingType.committee:
        return const Color(0xFF10B981);
      case MeetingType.emergency:
        return const Color(0xFFEF4444);
      case MeetingType.lineMeeting:
        return const Color(0xFF8B5CF6);
      case MeetingType.maintenance:
        return const Color(0xFFF59E0B);
      case MeetingType.festivalEvent:
        return const Color(0xFFEC4899);
    }
  }

  IconData _getMeetingTypeIcon(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return Icons.groups;
      case MeetingType.committee:
        return Icons.business;
      case MeetingType.emergency:
        return Icons.warning;
      case MeetingType.lineMeeting:
        return Icons.home_work;
      case MeetingType.maintenance:
        return Icons.build;
      case MeetingType.festivalEvent:
        return Icons.celebration;
    }
  }

  String _formatMeetingType(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return 'General Body Meeting';
      case MeetingType.committee:
        return 'Committee Meeting';
      case MeetingType.emergency:
        return 'Emergency Meeting';
      case MeetingType.lineMeeting:
        return 'Line Meeting';
      case MeetingType.maintenance:
        return 'Maintenance Meeting';
      case MeetingType.festivalEvent:
        return 'Festival/Event Planning';
    }
  }

  Color _getStatusColor(AttendeeStatus status) {
    switch (status) {
      case AttendeeStatus.accepted:
        return Colors.green;
      case AttendeeStatus.declined:
        return Colors.red;
      case AttendeeStatus.tentative:
        return Colors.orange;
      case AttendeeStatus.attended:
        return Colors.blue;
      case AttendeeStatus.absent:
        return Colors.grey;
      case AttendeeStatus.invited:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(AttendeeStatus status) {
    switch (status) {
      case AttendeeStatus.accepted:
        return Icons.check_circle;
      case AttendeeStatus.declined:
        return Icons.cancel;
      case AttendeeStatus.tentative:
        return Icons.help;
      case AttendeeStatus.attended:
        return Icons.person;
      case AttendeeStatus.absent:
        return Icons.person_off;
      case AttendeeStatus.invited:
        return Icons.mail;
    }
  }

  String _formatStatus(AttendeeStatus status) {
    switch (status) {
      case AttendeeStatus.accepted:
        return 'Accepted';
      case AttendeeStatus.declined:
        return 'Declined';
      case AttendeeStatus.tentative:
        return 'Tentative';
      case AttendeeStatus.attended:
        return 'Attended';
      case AttendeeStatus.absent:
        return 'Absent';
      case AttendeeStatus.invited:
        return 'Invited';
    }
  }
}
