import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:society_management/auth/service/auth_service.dart';
import 'package:society_management/broadcasting/model/broadcast_model.dart';
import 'package:society_management/broadcasting/repository/i_broadcast_repository.dart';
import 'package:society_management/broadcasting/service/broadcast_service.dart';
import 'package:society_management/constants/app_colors.dart';
import 'package:society_management/injector/injector.dart';
import 'package:society_management/users/model/user_model.dart';
import 'package:society_management/utility/extentions/navigation_extension.dart';
import 'package:society_management/utility/utility.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class CreateBroadcastPage extends StatefulWidget {
  final BroadcastType? initialType;

  const CreateBroadcastPage({super.key, this.initialType});

  @override
  State<CreateBroadcastPage> createState() => _CreateBroadcastPageState();
}

class _CreateBroadcastPageState extends State<CreateBroadcastPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  final _lineNumberController = TextEditingController();

  final BroadcastService _broadcastService = getIt<BroadcastService>();
  final AuthService _authService = getIt<AuthService>();

  UserModel? _currentUser;
  BroadcastType _selectedType = BroadcastType.announcement;
  BroadcastPriority _selectedPriority = BroadcastPriority.normal;
  BroadcastTarget _selectedTarget = BroadcastTarget.all;
  DateTime? _scheduledDateTime;
  bool _isLoading = false;
  bool _sendImmediately = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
      _setPriorityBasedOnType();
    }
  }

  void _setPriorityBasedOnType() {
    switch (_selectedType) {
      case BroadcastType.emergency:
        _selectedPriority = BroadcastPriority.critical;
        break;
      case BroadcastType.warning:
        _selectedPriority = BroadcastPriority.urgent;
        break;
      case BroadcastType.reminder:
        _selectedPriority = BroadcastPriority.high;
        break;
      default:
        _selectedPriority = BroadcastPriority.normal;
    }
  }

  Future<void> _loadCurrentUser() async {
    final user = await _authService.getCurrentUser();
    setState(() => _currentUser = user);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? Colors.grey[900] : Colors.grey[50],
      appBar: CommonAppBar(
        title: '✨ Create Broadcast',
        showDivider: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'Help',
          ),
        ],
      ),
      body: _currentUser == null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(color: AppColors.primaryBlue),
                  const Gap(16),
                  Text(
                    'Loading user information...',
                    style: TextStyle(
                      color: isDark ? Colors.grey[300] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeaderCard(context),
                    const Gap(20),
                    _buildTypeSection(),
                    const Gap(20),
                    _buildContentSection(),
                    const Gap(20),
                    _buildTargetSection(),
                    const Gap(20),
                    _buildSchedulingSection(),
                    const Gap(30),
                    _buildActionButtons(),
                    const Gap(20),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildTypeSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.category, color: AppColors.primaryBlue),
                Gap(8),
                Text(
                  'Broadcast Type',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Gap(12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: BroadcastType.values.map((type) {
                final isSelected = _selectedType == type;
                return InkWell(
                  onTap: () {
                    setState(() {
                      _selectedType = type;
                      _setPriorityBasedOnType();
                    });
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.primaryBlue : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? AppColors.primaryBlue : Colors.grey.shade300,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(type.emoji),
                        const Gap(4),
                        Text(
                          type.displayName,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            const Gap(12),
            Row(
              children: [
                const Text('Priority: ', style: TextStyle(fontWeight: FontWeight.w500)),
                DropdownButton<BroadcastPriority>(
                  value: _selectedPriority,
                  onChanged: (priority) {
                    if (priority != null) {
                      setState(() => _selectedPriority = priority);
                    }
                  },
                  items: BroadcastPriority.values.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: Text(priority.displayName),
                    );
                  }).toList(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.edit, color: AppColors.primaryBlue),
                Gap(8),
                Text(
                  'Content',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Gap(12),
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                hintText: 'Enter broadcast title',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const Gap(12),
            TextFormField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                hintText: 'Enter your message here...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a message';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.group, color: AppColors.primaryBlue),
                Gap(8),
                Text(
                  'Target Audience',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Gap(12),
            ...BroadcastTarget.values.map((target) {
              return RadioListTile<BroadcastTarget>(
                title: Text(target.displayName),
                value: target,
                groupValue: _selectedTarget,
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedTarget = value);
                  }
                },
                dense: true,
                contentPadding: EdgeInsets.zero,
              );
            }),
            if (_selectedTarget == BroadcastTarget.line) ...[
              const Gap(8),
              TextFormField(
                controller: _lineNumberController,
                decoration: const InputDecoration(
                  labelText: 'Line Number',
                  hintText: 'e.g., line_a, line_b',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (_selectedTarget == BroadcastTarget.line && (value == null || value.trim().isEmpty)) {
                    return 'Please enter line number';
                  }
                  return null;
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSchedulingSection() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.schedule, color: AppColors.primaryBlue),
                Gap(8),
                Text(
                  'Scheduling',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Gap(12),
            SwitchListTile(
              title: const Text('Send Immediately'),
              subtitle: Text(_sendImmediately ? 'Broadcast will be sent right away' : 'Schedule for later'),
              value: _sendImmediately,
              onChanged: (value) {
                setState(() => _sendImmediately = value);
              },
              contentPadding: EdgeInsets.zero,
            ),
            if (!_sendImmediately) ...[
              const Gap(8),
              ListTile(
                title: Text(_scheduledDateTime == null
                    ? 'Select Date & Time'
                    : 'Scheduled: ${_formatDateTime(_scheduledDateTime!)}'),
                trailing: const Icon(Icons.calendar_today),
                onTap: _selectDateTime,
                contentPadding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
        ),
        const Gap(12),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _createBroadcast,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(_sendImmediately ? 'Send Now' : 'Schedule'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(hours: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null) {
        setState(() {
          _scheduledDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _createBroadcast() async {
    if (!_formKey.currentState!.validate()) return;
    if (!_sendImmediately && _scheduledDateTime == null) {
      Utility.toast(message: 'Please select a date and time for scheduling');
      return;
    }

    setState(() => _isLoading = true);

    try {
      if (_sendImmediately) {
        // Create and send immediately
        await _broadcastService.createAndSendBroadcast(
          title: _titleController.text.trim(),
          message: _messageController.text.trim(),
          type: _selectedType,
          priority: _selectedPriority,
          target: _selectedTarget,
          targetLineNumber: _selectedTarget == BroadcastTarget.line ? _lineNumberController.text.trim() : null,
          createdBy: _currentUser!.id!,
          creatorName: _currentUser!.name ?? 'Unknown',
        );

        Utility.toast(message: 'Broadcast sent successfully!');
      } else {
        // Create and schedule
        final createResult = await getIt<IBroadcastRepository>().createBroadcast(
          title: _titleController.text.trim(),
          message: _messageController.text.trim(),
          type: _selectedType,
          priority: _selectedPriority,
          target: _selectedTarget,
          targetLineNumber: _selectedTarget == BroadcastTarget.line ? _lineNumberController.text.trim() : null,
          createdBy: _currentUser!.id!,
          creatorName: _currentUser!.name ?? 'Unknown',
          scheduledAt: _scheduledDateTime,
        );

        createResult.fold(
          (failure) => throw Exception(failure.message),
          (broadcast) async {
            await _broadcastService.scheduleBroadcast(
              broadcastId: broadcast.id!,
              scheduledAt: _scheduledDateTime!,
            );
          },
        );

        Utility.toast(message: 'Broadcast scheduled successfully!');
      }

      if (mounted) {
        context.pop(true);
      }
    } catch (e) {
      Utility.toast(message: 'Error creating broadcast: $e');
    }

    setState(() => _isLoading = false);
  }

  // New improved methods
  Widget _buildHeaderCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryBlue.withValues(alpha: 0.8), AppColors.primaryPurple.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.create_rounded,
              color: Colors.white,
              size: 28,
            ),
          ),
          const Gap(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create New Broadcast',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Gap(4),
                Text(
                  'Share important information with your community',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: AppColors.primaryBlue),
            Gap(8),
            Text('Broadcasting Help'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('📢 Announcement: General community news'),
            Gap(8),
            Text('🚨 Emergency: Critical urgent alerts'),
            Gap(8),
            Text('🔧 Maintenance: Service notifications'),
            Gap(8),
            Text('🎉 Event: Invitations and celebrations'),
            Gap(8),
            Text('⏰ Reminder: Payment and meeting reminders'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    _lineNumberController.dispose();
    super.dispose();
  }
}
