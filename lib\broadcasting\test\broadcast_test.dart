import 'package:society_management/broadcasting/model/broadcast_model.dart';
import 'package:society_management/broadcasting/repository/broadcast_repository.dart';
import 'package:society_management/broadcasting/service/broadcast_service.dart';

/// Simple test to verify broadcasting system works
class BroadcastTest {
  static void testBroadcastModel() {
    print('Testing BroadcastModel...');
    
    try {
      // Test creating a broadcast model
      final broadcast = BroadcastModel(
        title: 'Test Broadcast',
        message: 'This is a test message',
        type: BroadcastType.announcement,
        priority: BroadcastPriority.normal,
        target: BroadcastTarget.all,
        createdBy: 'test_user',
        creatorName: 'Test User',
        createdAt: DateTime.now(),
        status: BroadcastStatus.draft,
      );
      
      print('✅ BroadcastModel created successfully');
      print('   Title: ${broadcast.title}');
      print('   Type: ${broadcast.type.displayName}');
      print('   Priority: ${broadcast.priority.displayName}');
      print('   Target: ${broadcast.target.displayName}');
      
      // Test enum extensions
      print('   Type emoji: ${broadcast.type.emoji}');
      
    } catch (e) {
      print('❌ Error testing BroadcastModel: $e');
    }
  }
  
  static void testBroadcastRepository() {
    print('\nTesting BroadcastRepository...');
    
    try {
      // Test creating repository instance
      final repository = BroadcastRepository();
      print('✅ BroadcastRepository created successfully');
      
    } catch (e) {
      print('❌ Error testing BroadcastRepository: $e');
    }
  }
  
  static void testBroadcastService() {
    print('\nTesting BroadcastService...');
    
    try {
      // Test creating service instance
      final repository = BroadcastRepository();
      final service = BroadcastService(repository);
      print('✅ BroadcastService created successfully');
      
    } catch (e) {
      print('❌ Error testing BroadcastService: $e');
    }
  }
  
  static void runAllTests() {
    print('🧪 Running Broadcasting System Tests...\n');
    
    testBroadcastModel();
    testBroadcastRepository();
    testBroadcastService();
    
    print('\n✅ All broadcasting tests completed!');
  }
}
