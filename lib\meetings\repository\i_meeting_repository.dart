import 'package:fpdart/fpdart.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/utility/failure.dart';

abstract class IMeetingRepository {
  /// Create a new meeting
  Future<Either<Failure, String>> createMeeting(MeetingModel meeting);

  /// Update an existing meeting
  Future<Either<Failure, void>> updateMeeting(MeetingModel meeting);

  /// Delete a meeting
  Future<Either<Failure, void>> deleteMeeting(String meetingId);

  /// Get meeting by ID
  Future<Either<Failure, MeetingModel>> getMeetingById(String meetingId);

  /// Get all meetings
  Future<Either<Failure, List<MeetingModel>>> getAllMeetings();

  /// Get meetings by organizer
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByOrganizer(String organizerId);

  /// Get meetings by attendee
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByAttendee(String userId);

  /// Get meetings by line number
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByLine(String lineNumber);

  /// Get meetings by type
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByType(MeetingType type);

  /// Get meetings by status
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByStatus(MeetingStatus status);

  /// Get upcoming meetings
  Future<Either<Failure, List<MeetingModel>>> getUpcomingMeetings();

  /// Get meetings for a specific date range
  Future<Either<Failure, List<MeetingModel>>> getMeetingsByDateRange(
    DateTime startDate,
    DateTime endDate,
  );

  /// Update attendee response (RSVP)
  Future<Either<Failure, void>> updateAttendeeResponse(
    String meetingId,
    String userId,
    AttendeeStatus status,
  );

  /// Add meeting minutes
  Future<Either<Failure, void>> addMeetingMinutes(
    String meetingId,
    String minutes,
  );

  /// Add action item to meeting
  Future<Either<Failure, void>> addActionItem(
    String meetingId,
    ActionItem actionItem,
  );

  /// Update action item status
  Future<Either<Failure, void>> updateActionItemStatus(
    String meetingId,
    String actionItemId,
    bool isCompleted,
  );

  /// Get action items for a user
  Future<Either<Failure, List<ActionItem>>> getActionItemsForUser(String userId);

  /// Get pending action items
  Future<Either<Failure, List<ActionItem>>> getPendingActionItems();

  /// Update meeting status
  Future<Either<Failure, void>> updateMeetingStatus(
    String meetingId,
    MeetingStatus status,
  );

  /// Get meeting statistics
  Future<Either<Failure, Map<String, dynamic>>> getMeetingStatistics();

  /// Search meetings by title or description
  Future<Either<Failure, List<MeetingModel>>> searchMeetings(String query);

  /// Get recurring meetings
  Future<Either<Failure, List<MeetingModel>>> getRecurringMeetings();

  /// Cancel meeting
  Future<Either<Failure, void>> cancelMeeting(String meetingId, String reason);

  /// Postpone meeting
  Future<Either<Failure, void>> postponeMeeting(
    String meetingId,
    DateTime newDate,
    DateTime newStartTime,
    DateTime newEndTime,
  );
}
