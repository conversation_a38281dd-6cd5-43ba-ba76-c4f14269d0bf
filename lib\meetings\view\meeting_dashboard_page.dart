import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:society_management/injector/injector.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/repository/i_meeting_repository.dart';
import 'package:society_management/meetings/view/create_meeting_page.dart';
import 'package:society_management/meetings/view/meeting_details_page.dart';
import 'package:society_management/meetings/view/meeting_rsvp_page.dart';
import 'package:society_management/theme/theme_utils.dart';
import 'package:society_management/users/model/user_model.dart';
import 'package:society_management/users/repository/i_user_repository.dart';
import 'package:society_management/utility/utility.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class MeetingDashboardPage extends StatefulWidget {
  const MeetingDashboardPage({super.key});

  @override
  State<MeetingDashboardPage> createState() => _MeetingDashboardPageState();
}

class _MeetingDashboardPageState extends State<MeetingDashboardPage> {
  final IMeetingRepository _meetingRepository = getIt<IMeetingRepository>();
  final IUserRepository _userRepository = getIt<IUserRepository>();

  bool _isLoading = false;
  List<MeetingModel> _upcomingMeetings = [];
  List<MeetingModel> _recentMeetings = [];
  UserModel? _currentUser;
  Map<String, dynamic> _statistics = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load current user
      final userResult = await _userRepository.getCurrentUser();
      userResult.fold(
        (failure) => Utility.toast(message: 'Error loading user: ${failure.message}'),
        (user) => _currentUser = user,
      );

      // Load upcoming meetings
      final upcomingResult = await _meetingRepository.getUpcomingMeetings();
      upcomingResult.fold(
        (failure) => Utility.toast(message: 'Error loading upcoming meetings: ${failure.message}'),
        (meetings) => _upcomingMeetings = meetings.take(5).toList(),
      );

      // Load recent meetings
      final allMeetingsResult = await _meetingRepository.getAllMeetings();
      allMeetingsResult.fold(
        (failure) => Utility.toast(message: 'Error loading meetings: ${failure.message}'),
        (meetings) {
          _recentMeetings = meetings.where((m) => m.status == MeetingStatus.completed).take(5).toList();
        },
      );

      // Load statistics
      final statsResult = await _meetingRepository.getMeetingStatistics();
      statsResult.fold(
        (failure) => {},
        (stats) => _statistics = stats,
      );
    } catch (e) {
      Utility.toast(message: 'Error loading data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ThemeUtils.isDarkMode(context);

    return Scaffold(
      appBar: CommonAppBar(
        title: 'Meeting Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.science),
            onPressed: _createDemoMeetings,
            tooltip: 'Create Demo Meetings',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_month),
            onPressed: _showCalendarView,
            tooltip: 'Calendar View',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToCreateMeeting(),
        icon: const Icon(Icons.add),
        label: const Text('Schedule Meeting'),
        backgroundColor: const Color(0xFF4F46E5),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                : [const Color(0xFFF8FAFC), const Color(0xFFE2E8F0)],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      _buildHeaderSection(context),
                      const SizedBox(height: 24),

                      // Statistics Cards
                      _buildStatisticsSection(context),
                      const SizedBox(height: 24),

                      // Quick Actions
                      _buildQuickActionsSection(context),
                      const SizedBox(height: 24),

                      // Upcoming Meetings
                      _buildUpcomingMeetingsSection(context),
                      const SizedBox(height: 24),

                      // Recent Meetings
                      _buildRecentMeetingsSection(context),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4F46E5).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.event,
                    color: Color(0xFF4F46E5),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Meeting Dashboard',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Manage society meetings, schedules, and agendas',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Meeting Statistics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.1,
          children: [
            _buildStatCard(
              'Total Meetings',
              '${_statistics['totalMeetings'] ?? 0}',
              Icons.event,
              const Color(0xFF3B82F6),
            ),
            _buildStatCard(
              'Upcoming',
              '${_statistics['upcomingMeetings'] ?? 0}',
              Icons.schedule,
              const Color(0xFF10B981),
            ),
            _buildStatCard(
              'Completed',
              '${_statistics['completedMeetings'] ?? 0}',
              Icons.check_circle,
              const Color(0xFF8B5CF6),
            ),
            _buildStatCard(
              'Action Items',
              '${_statistics['pendingActionItems'] ?? 0}',
              Icons.task,
              const Color(0xFFF59E0B),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return ThemeAwareCard(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color, color.withOpacity(0.8)],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'Schedule Meeting',
                'Create new meeting',
                Icons.add_circle,
                const Color(0xFF4F46E5),
                () => _navigateToCreateMeeting(),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildQuickActionCard(
                'View Calendar',
                'Meeting calendar',
                Icons.calendar_month,
                const Color(0xFF10B981),
                () => _showCalendarView(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ThemeAwareCard(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingMeetingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Upcoming Meetings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            TextButton(
              onPressed: () => _showAllMeetings(),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_upcomingMeetings.isEmpty)
          _buildEmptyState('No upcoming meetings', Icons.event_available)
        else
          ..._upcomingMeetings.map((meeting) => _buildMeetingCard(meeting)),
      ],
    );
  }

  Widget _buildRecentMeetingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Meetings',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        if (_recentMeetings.isEmpty)
          _buildEmptyState('No recent meetings', Icons.history)
        else
          ..._recentMeetings.map((meeting) => _buildMeetingCard(meeting)),
      ],
    );
  }

  Widget _buildMeetingCard(MeetingModel meeting) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: ThemeAwareCard(
        onTap: () => _navigateToMeetingDetails(meeting),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getMeetingTypeColor(meeting.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getMeetingTypeIcon(meeting.type),
                      color: _getMeetingTypeColor(meeting.type),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          meeting.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        Text(
                          _formatMeetingType(meeting.type),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: _getMeetingTypeColor(meeting.type),
                              ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(meeting.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _formatStatus(meeting.status),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('MMM dd, yyyy • hh:mm a').format(meeting.startTime),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      meeting.venue,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              // RSVP Button for scheduled meetings
              if (meeting.status == MeetingStatus.scheduled) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _navigateToRSVP(meeting),
                        icon: const Icon(Icons.thumb_up, size: 16),
                        label: const Text('Will You Come?'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          side: BorderSide(color: _getMeetingTypeColor(meeting.type)),
                          foregroundColor: _getMeetingTypeColor(meeting.type),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _navigateToMeetingDetails(meeting),
                        icon: const Icon(Icons.info_outline, size: 16),
                        label: const Text('Details'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          backgroundColor: _getMeetingTypeColor(meeting.type),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              icon,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getMeetingTypeColor(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return const Color(0xFF3B82F6);
      case MeetingType.committee:
        return const Color(0xFF10B981);
      case MeetingType.emergency:
        return const Color(0xFFEF4444);
      case MeetingType.lineMeeting:
        return const Color(0xFF8B5CF6);
      case MeetingType.maintenance:
        return const Color(0xFFF59E0B);
      case MeetingType.festivalEvent:
        return const Color(0xFFEC4899);
    }
  }

  IconData _getMeetingTypeIcon(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return Icons.groups;
      case MeetingType.committee:
        return Icons.business;
      case MeetingType.emergency:
        return Icons.warning;
      case MeetingType.lineMeeting:
        return Icons.home_work;
      case MeetingType.maintenance:
        return Icons.build;
      case MeetingType.festivalEvent:
        return Icons.celebration;
    }
  }

  String _formatMeetingType(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return 'General Body Meeting';
      case MeetingType.committee:
        return 'Committee Meeting';
      case MeetingType.emergency:
        return 'Emergency Meeting';
      case MeetingType.lineMeeting:
        return 'Line Meeting';
      case MeetingType.maintenance:
        return 'Maintenance Meeting';
      case MeetingType.festivalEvent:
        return 'Festival/Event Planning';
    }
  }

  Color _getStatusColor(MeetingStatus status) {
    switch (status) {
      case MeetingStatus.scheduled:
        return const Color(0xFF3B82F6);
      case MeetingStatus.ongoing:
        return const Color(0xFF10B981);
      case MeetingStatus.completed:
        return const Color(0xFF8B5CF6);
      case MeetingStatus.cancelled:
        return const Color(0xFFEF4444);
      case MeetingStatus.postponed:
        return const Color(0xFFF59E0B);
    }
  }

  String _formatStatus(MeetingStatus status) {
    switch (status) {
      case MeetingStatus.scheduled:
        return 'Scheduled';
      case MeetingStatus.ongoing:
        return 'Ongoing';
      case MeetingStatus.completed:
        return 'Completed';
      case MeetingStatus.cancelled:
        return 'Cancelled';
      case MeetingStatus.postponed:
        return 'Postponed';
    }
  }

  void _navigateToCreateMeeting() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateMeetingPage(),
      ),
    );

    // Refresh data if meeting was created successfully
    if (result == true) {
      _loadData();
    }
  }

  void _navigateToMeetingDetails(MeetingModel meeting) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MeetingDetailsPage(meeting: meeting),
      ),
    );
  }

  void _navigateToRSVP(MeetingModel meeting) async {
    try {
      // Navigate to RSVP page
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MeetingRSVPPage(meeting: meeting),
        ),
      );

      // If RSVP was updated, refresh the dashboard
      if (result == true && mounted) {
        _loadData();
      }
    } catch (e) {
      // Fallback to showing a message if navigation fails
      if (mounted) {
        Utility.toast(message: 'Unable to open RSVP page: $e');
      }
    }
  }

  void _createDemoMeetings() async {
    try {
      Utility.toast(message: 'Creating demo meetings...');

      // Create sample meetings with RSVP data
      await _createSampleMeetings();

      // Refresh the dashboard to show new meetings
      _loadData();

      Utility.toast(message: '✅ Demo meetings created! You can now test RSVP feature.');
    } catch (e) {
      Utility.toast(message: 'Error creating demo meetings: $e');
    }
  }

  Future<void> _createSampleMeetings() async {
    final now = DateTime.now();

    // Get current user
    final userResult = await _userRepository.getCurrentUser();
    final currentUser = userResult.fold(
      (failure) => throw Exception('Failed to get current user'),
      (user) => user,
    );

    // Sample Meeting 1: General Body Meeting (Tomorrow)
    final meeting1 = MeetingModel(
      title: "Monthly General Body Meeting",
      description:
          "Discuss society maintenance, upcoming events, and member concerns. All residents are encouraged to attend.",
      type: MeetingType.generalBody,
      scheduledDate: now.add(const Duration(days: 1)),
      startTime: DateTime(now.year, now.month, now.day + 1, 18, 0),
      endTime: DateTime(now.year, now.month, now.day + 1, 20, 0),
      venue: "Community Hall - Ground Floor",
      organizer: currentUser.id ?? '',
      organizerName: currentUser.name ?? 'Admin',
      invitedUsers: [],
      attendees: [
        AttendeeInfo(
          userId: currentUser.id ?? '',
          userName: currentUser.name ?? 'Admin',
          userEmail: currentUser.email ?? '',
          role: currentUser.role ?? 'Admin',
          status: AttendeeStatus.accepted,
          responseDate: now,
        ),
        AttendeeInfo(
          userId: "demo_user_1",
          userName: "John Doe",
          userEmail: "<EMAIL>",
          lineNumber: "A-101",
          role: "Line Head",
          status: AttendeeStatus.accepted,
          responseDate: now.subtract(const Duration(hours: 2)),
        ),
        AttendeeInfo(
          userId: "demo_user_2",
          userName: "Jane Smith",
          userEmail: "<EMAIL>",
          lineNumber: "B-205",
          role: "Member",
          status: AttendeeStatus.tentative,
          responseDate: now.subtract(const Duration(hours: 1)),
          responseNote: "Will try to attend if work permits",
        ),
        AttendeeInfo(
          userId: "demo_user_3",
          userName: "Mike Johnson",
          userEmail: "<EMAIL>",
          lineNumber: "C-301",
          role: "Member",
          status: AttendeeStatus.declined,
          responseDate: now.subtract(const Duration(minutes: 30)),
          responseNote: "Out of town for business",
        ),
        AttendeeInfo(
          userId: "demo_user_4",
          userName: "Sarah Wilson",
          userEmail: "<EMAIL>",
          lineNumber: "A-102",
          role: "Member",
          status: AttendeeStatus.invited,
        ),
      ],
      lineNumbers: [],
      status: MeetingStatus.scheduled,
      agenda: [],
      actionItems: [],
      isRecurring: false,
      createdAt: now.subtract(const Duration(days: 2)),
      updatedAt: now.subtract(const Duration(hours: 1)),
      attendeeResponses: {
        currentUser.id ?? '': AttendeeStatus.accepted,
        "demo_user_1": AttendeeStatus.accepted,
        "demo_user_2": AttendeeStatus.tentative,
        "demo_user_3": AttendeeStatus.declined,
      },
      allowRSVP: true,
      rsvpDeadline: DateTime(now.year, now.month, now.day + 1, 16, 0),
      isLive: false,
      liveData: {},
    );

    // Create the meeting
    final result = await _meetingRepository.createMeeting(meeting1);
    result.fold(
      (failure) => throw Exception('Failed to create demo meeting: ${failure.message}'),
      (_) => print('Demo meeting created successfully'),
    );
  }

  void _showCalendarView() {
    // TODO: Implement calendar view
    Utility.toast(message: 'Calendar view - Coming Soon!');
  }

  void _showAllMeetings() {
    // TODO: Implement all meetings view
    Utility.toast(message: 'All meetings view - Coming Soon!');
  }
}
