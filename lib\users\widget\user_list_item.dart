import 'package:flutter/material.dart';
import 'package:society_management/constants/app_colors.dart';
import 'package:society_management/users/model/user_model.dart';
import 'package:society_management/users/view/add_user_page.dart';
import 'package:society_management/utility/extentions/navigation_extension.dart';
import 'package:society_management/users/widget/user_info_widget.dart';

/// A reusable widget for displaying user information in a list
class UserListItem extends StatelessWidget {
  final UserModel user;
  final VoidCallback? onEdit;
  final VoidCallback? onResetPassword;
  final VoidCallback? onRefresh;

  const UserListItem({
    super.key,
    required this.user,
    this.onEdit,
    this.onResetPassword,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: isDarkMode ? AppColors.darkCard : AppColors.lightContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getBorderColor(isDarkMode),
          width: user.isAdmin || user.isLineHead ? 1.5 : 1.0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info section
            UserInfoWidget(user: user),
            
            const SizedBox(height: 12),
            
            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// Get border color based on user role and theme
  Color _getBorderColor(bool isDarkMode) {
    if (user.isAdmin) {
      return Colors.red.withAlpha(isDarkMode ? 100 : 150);
    } else if (user.isLineHead) {
      return Colors.blue.withAlpha(isDarkMode ? 100 : 150);
    } else {
      return isDarkMode 
          ? Colors.white.withAlpha(25) 
          : AppColors.lightDivider;
    }
  }

  /// Build action buttons row
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Edit button
        TextButton.icon(
          onPressed: () async {
            await context.push(AddUserPage(userId: user.id));
            onRefresh?.call();
          },
          icon: const Icon(Icons.edit, size: 18),
          label: const Text('Edit'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.blue,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Reset Password button
        TextButton.icon(
          onPressed: onResetPassword,
          icon: const Icon(Icons.lock_reset, size: 18),
          label: const Text('Reset Password'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.amber,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }
}
