import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:society_management/dashboard/cubit/dashboard_cubit.dart';

/// Example widget showing how to use Dashboard Cubit
/// This widget automatically updates when dashboard stats change
class DashboardStatsWidget extends StatelessWidget {
  const DashboardStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state.error != null) {
          return Card(
            color: Colors.red.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.error, color: Colors.red, size: 32),
                  const SizedBox(height: 8),
                  Text(
                    'Error: ${state.error}',
                    style: TextStyle(color: Colors.red.shade700),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      context.read<DashboardCubit>().clearError();
                      context.read<DashboardCubit>().refresh();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Dashboard Stats',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => context.read<DashboardCubit>().refresh(),
                      icon: const Icon(Icons.refresh),
                      tooltip: 'Refresh Stats',
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Total Expenses
                _buildStatRow(
                  icon: Icons.account_balance_wallet,
                  label: 'Total Expenses',
                  value: _formatCurrency(state.totalExpenses),
                  color: Colors.red,
                ),
                
                // Total Members
                _buildStatRow(
                  icon: Icons.people,
                  label: 'Total Members',
                  value: state.totalMembers.toString(),
                  color: Colors.blue,
                ),
                
                // Maintenance Collected
                _buildStatRow(
                  icon: Icons.check_circle,
                  label: 'Maintenance Collected',
                  value: _formatCurrency(state.maintenanceCollected),
                  color: Colors.green,
                ),
                
                // Maintenance Pending
                _buildStatRow(
                  icon: Icons.pending,
                  label: 'Maintenance Pending',
                  value: _formatCurrency(state.maintenancePending),
                  color: Colors.orange,
                ),
                
                // Fully Paid Users
                _buildStatRow(
                  icon: Icons.verified,
                  label: 'Fully Paid Users',
                  value: state.fullyPaidUsers.toString(),
                  color: Colors.purple,
                ),
                
                if (state.lastUpdated != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Last updated: ${_formatDateTime(state.lastUpdated!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      symbol: '₹',
      decimalDigits: 0,
      locale: 'en_IN',
    );
    return formatter.format(amount);
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('MMM dd, yyyy HH:mm').format(dateTime);
  }
}

/// Simple action buttons for testing Dashboard Cubit
class DashboardActionButtons extends StatelessWidget {
  const DashboardActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dashboard Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Expense Actions
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => context.read<DashboardCubit>().addExpense(1000),
                  icon: const Icon(Icons.add),
                  label: const Text('Add ₹1000 Expense'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.read<DashboardCubit>().removeExpense(500),
                  icon: const Icon(Icons.remove),
                  label: const Text('Remove ₹500 Expense'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.read<DashboardCubit>().addUser(),
                  icon: const Icon(Icons.person_add),
                  label: const Text('Add User'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.read<DashboardCubit>().removeUser(),
                  icon: const Icon(Icons.person_remove),
                  label: const Text('Remove User'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.read<DashboardCubit>().updateMaintenance(
                    collectedChange: 2000,
                    fullyPaidChange: 1,
                  ),
                  icon: const Icon(Icons.update),
                  label: const Text('Update Maintenance'),
                ),
                ElevatedButton.icon(
                  onPressed: () => context.read<DashboardCubit>().refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh All'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Example page showing how to use Dashboard Cubit
class DashboardExamplePage extends StatelessWidget {
  const DashboardExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Cubit Example'),
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            DashboardStatsWidget(),
            SizedBox(height: 16),
            DashboardActionButtons(),
          ],
        ),
      ),
    );
  }
}
