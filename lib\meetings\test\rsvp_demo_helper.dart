import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/repository/i_meeting_repository.dart';
import 'package:society_management/injector/injector.dart';

class RSVPDemoHelper {
  static final IMeetingRepository _meetingRepository = getIt<IMeetingRepository>();

  /// Creates sample meetings to demonstrate RSVP functionality
  static Future<void> createSampleMeetings() async {
    final now = DateTime.now();
    
    // Sample Meeting 1: General Body Meeting (Tomorrow)
    final meeting1 = MeetingModel(
      title: "Monthly General Body Meeting",
      description: "Discuss society maintenance, upcoming events, and member concerns. All residents are encouraged to attend.",
      type: MeetingType.generalBody,
      scheduledDate: now.add(const Duration(days: 1)),
      startTime: DateTime(now.year, now.month, now.day + 1, 18, 0), // 6 PM tomorrow
      endTime: DateTime(now.year, now.month, now.day + 1, 20, 0),   // 8 PM tomorrow
      venue: "Community Hall - Ground Floor",
      organizer: "admin123",
      organizerName: "Society Admin",
      invitedUsers: [],
      attendees: [
        AttendeeInfo(
          userId: "admin123",
          userName: "Society Admin",
          userEmail: "<EMAIL>",
          role: "Admin",
          status: AttendeeStatus.accepted,
          responseDate: now,
        ),
        AttendeeInfo(
          userId: "user001",
          userName: "John Doe",
          userEmail: "<EMAIL>",
          lineNumber: "A-101",
          role: "Line Head",
          status: AttendeeStatus.accepted,
          responseDate: now.subtract(const Duration(hours: 2)),
        ),
        AttendeeInfo(
          userId: "user002",
          userName: "Jane Smith",
          userEmail: "<EMAIL>",
          lineNumber: "B-205",
          role: "Member",
          status: AttendeeStatus.tentative,
          responseDate: now.subtract(const Duration(hours: 1)),
          responseNote: "Will try to attend if work permits",
        ),
        AttendeeInfo(
          userId: "user003",
          userName: "Mike Johnson",
          userEmail: "<EMAIL>",
          lineNumber: "C-301",
          role: "Member",
          status: AttendeeStatus.declined,
          responseDate: now.subtract(const Duration(minutes: 30)),
          responseNote: "Out of town for business",
        ),
        AttendeeInfo(
          userId: "user004",
          userName: "Sarah Wilson",
          userEmail: "<EMAIL>",
          lineNumber: "A-102",
          role: "Member",
          status: AttendeeStatus.invited, // No response yet
        ),
      ],
      lineNumbers: [],
      status: MeetingStatus.scheduled,
      agenda: [
        AgendaItem(
          id: "1",
          title: "Maintenance Updates",
          description: "Review of ongoing maintenance work",
          duration: 15,
          presenter: "Maintenance Head",
        ),
        AgendaItem(
          id: "2", 
          title: "Festival Planning",
          description: "Upcoming Diwali celebration planning",
          duration: 20,
          presenter: "Cultural Committee",
        ),
      ],
      actionItems: [],
      isRecurring: false,
      createdAt: now.subtract(const Duration(days: 2)),
      updatedAt: now.subtract(const Duration(hours: 1)),
      attendeeResponses: {
        "admin123": AttendeeStatus.accepted,
        "user001": AttendeeStatus.accepted,
        "user002": AttendeeStatus.tentative,
        "user003": AttendeeStatus.declined,
      },
      allowRSVP: true,
      rsvpDeadline: DateTime(now.year, now.month, now.day + 1, 16, 0), // 4 PM tomorrow
      isLive: false,
      liveData: {},
    );

    // Sample Meeting 2: Emergency Meeting (This Evening)
    final meeting2 = MeetingModel(
      title: "Emergency: Water Supply Issue",
      description: "Urgent discussion about the water supply disruption and immediate action plan.",
      type: MeetingType.emergency,
      scheduledDate: now,
      startTime: DateTime(now.year, now.month, now.day, 19, 0), // 7 PM today
      endTime: DateTime(now.year, now.month, now.day, 20, 30),  // 8:30 PM today
      venue: "Society Office",
      organizer: "admin123",
      organizerName: "Society Admin",
      invitedUsers: [],
      attendees: [
        AttendeeInfo(
          userId: "admin123",
          userName: "Society Admin", 
          userEmail: "<EMAIL>",
          role: "Admin",
          status: AttendeeStatus.accepted,
          responseDate: now.subtract(const Duration(hours: 3)),
        ),
        AttendeeInfo(
          userId: "user001",
          userName: "John Doe",
          userEmail: "<EMAIL>",
          lineNumber: "A-101",
          role: "Line Head",
          status: AttendeeStatus.accepted,
          responseDate: now.subtract(const Duration(hours: 2)),
        ),
        AttendeeInfo(
          userId: "user005",
          userName: "Robert Brown",
          userEmail: "<EMAIL>",
          lineNumber: "B-201",
          role: "Line Head",
          status: AttendeeStatus.invited, // No response yet
        ),
      ],
      lineNumbers: ["A", "B"],
      status: MeetingStatus.scheduled,
      agenda: [
        AgendaItem(
          id: "1",
          title: "Water Supply Status",
          description: "Current status and expected resolution time",
          duration: 10,
          presenter: "Maintenance Team",
        ),
        AgendaItem(
          id: "2",
          title: "Immediate Actions",
          description: "Steps to manage the situation",
          duration: 15,
          presenter: "Admin Team",
        ),
      ],
      actionItems: [],
      isRecurring: false,
      createdAt: now.subtract(const Duration(hours: 4)),
      updatedAt: now.subtract(const Duration(hours: 1)),
      attendeeResponses: {
        "admin123": AttendeeStatus.accepted,
        "user001": AttendeeStatus.accepted,
      },
      allowRSVP: true,
      rsvpDeadline: DateTime(now.year, now.month, now.day, 18, 0), // 6 PM today
      isLive: false,
      liveData: {},
    );

    // Sample Meeting 3: Line A Meeting (Next Week)
    final meeting3 = MeetingModel(
      title: "Line A Monthly Meeting",
      description: "Monthly discussion for Line A residents about maintenance, parking, and common area usage.",
      type: MeetingType.lineMeeting,
      scheduledDate: now.add(const Duration(days: 7)),
      startTime: DateTime(now.year, now.month, now.day + 7, 17, 0), // 5 PM next week
      endTime: DateTime(now.year, now.month, now.day + 7, 18, 30),  // 6:30 PM next week
      venue: "Line A Common Area",
      organizer: "user001",
      organizerName: "John Doe",
      invitedUsers: [],
      attendees: [
        AttendeeInfo(
          userId: "user001",
          userName: "John Doe",
          userEmail: "<EMAIL>",
          lineNumber: "A-101",
          role: "Line Head",
          status: AttendeeStatus.accepted,
          responseDate: now.subtract(const Duration(days: 1)),
        ),
        AttendeeInfo(
          userId: "user004",
          userName: "Sarah Wilson",
          userEmail: "<EMAIL>",
          lineNumber: "A-102",
          role: "Member",
          status: AttendeeStatus.invited, // No response yet
        ),
        AttendeeInfo(
          userId: "user006",
          userName: "David Lee",
          userEmail: "<EMAIL>",
          lineNumber: "A-103",
          role: "Member",
          status: AttendeeStatus.accepted,
          responseDate: now.subtract(const Duration(hours: 12)),
        ),
      ],
      lineNumbers: ["A"],
      status: MeetingStatus.scheduled,
      agenda: [
        AgendaItem(
          id: "1",
          title: "Parking Issues",
          description: "Discuss visitor parking allocation",
          duration: 20,
          presenter: "Line Head",
        ),
      ],
      actionItems: [],
      isRecurring: true,
      recurringPattern: "monthly",
      createdAt: now.subtract(const Duration(days: 3)),
      updatedAt: now.subtract(const Duration(hours: 12)),
      attendeeResponses: {
        "user001": AttendeeStatus.accepted,
        "user006": AttendeeStatus.accepted,
      },
      allowRSVP: true,
      rsvpDeadline: DateTime(now.year, now.month, now.day + 6, 23, 59), // Day before meeting
      isLive: false,
      liveData: {},
    );

    // Create the meetings
    try {
      await _meetingRepository.createMeeting(meeting1);
      await _meetingRepository.createMeeting(meeting2);
      await _meetingRepository.createMeeting(meeting3);
      
      print("✅ Sample meetings created successfully!");
      print("📅 Meeting 1: General Body Meeting (Tomorrow 6 PM)");
      print("🚨 Meeting 2: Emergency Meeting (Today 7 PM)");
      print("🏠 Meeting 3: Line A Meeting (Next Week 5 PM)");
      
    } catch (e) {
      print("❌ Error creating sample meetings: $e");
    }
  }

  /// Shows RSVP statistics for demo purposes
  static void showRSVPStats() {
    print("\n📊 RSVP Feature Demo Statistics:");
    print("═══════════════════════════════════");
    print("🎯 General Body Meeting:");
    print("   ✅ Accepted: 2 people");
    print("   ❓ Tentative: 1 person");
    print("   ❌ Declined: 1 person");
    print("   ⏳ Pending: 1 person");
    print("");
    print("🚨 Emergency Meeting:");
    print("   ✅ Accepted: 2 people");
    print("   ⏳ Pending: 1 person");
    print("");
    print("🏠 Line A Meeting:");
    print("   ✅ Accepted: 2 people");
    print("   ⏳ Pending: 1 person");
    print("═══════════════════════════════════");
  }
}
