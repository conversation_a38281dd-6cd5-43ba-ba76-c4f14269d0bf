import 'package:flutter/material.dart';
import 'package:society_management/users/model/user_model.dart';
import 'package:society_management/utility/extentions/colors_extnetions.dart';
import 'package:society_management/users/widget/user_list_item.dart';

/// A reusable widget for displaying a list of users with loading, error, and empty states
class UserListWidget extends StatelessWidget {
  final ValueNotifier<bool> isLoading;
  final ValueNotifier<List<UserModel>> users;
  final ValueNotifier<String?> errorMessage;
  final VoidCallback? onRetry;
  final Function(UserModel)? onResetPassword;
  final VoidCallback? onRefresh;

  const UserListWidget({
    super.key,
    required this.isLoading,
    required this.users,
    required this.errorMessage,
    this.onRetry,
    this.onResetPassword,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: isLoading,
      builder: (_, isLoadingValue, __) {
        if (isLoadingValue) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        return ValueListenableBuilder<String?>(
          valueListenable: errorMessage,
          builder: (_, errorValue, __) {
            if (errorValue != null) {
              return _buildErrorState(errorValue);
            }
            return ValueListenableBuilder<List<UserModel>>(
              valueListenable: users,
              builder: (_, usersValue, __) {
                if (usersValue.isEmpty) {
                  return _buildEmptyState();
                }
                return _buildUsersList(usersValue);
              },
            );
          },
        );
      },
    );
  }

  /// Build error state widget
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.withOpacity2(0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'Error: $error',
            style: const TextStyle(
              color: Colors.red,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state widget
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey.withOpacity2(0.6),
          ),
          const SizedBox(height: 16),
          const Text(
            'No users found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add some users to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// Build users list
  Widget _buildUsersList(List<UserModel> usersList) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: usersList.length,
      itemBuilder: (context, index) {
        final user = usersList[index];
        return UserListItem(
          user: user,
          onResetPassword: onResetPassword != null ? () => onResetPassword!(user) : null,
          onRefresh: onRefresh,
        );
      },
    );
  }
}
