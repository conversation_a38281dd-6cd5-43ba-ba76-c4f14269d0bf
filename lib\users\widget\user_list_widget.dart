import 'package:flutter/material.dart';
import 'package:society_management/constants/app_colors.dart';
import 'package:society_management/users/model/user_model.dart';
import 'package:society_management/users/widget/user_list_item.dart';

/// A high-quality paginated user list widget with proper state management
/// Features: Pagination (10 items per page), proper color usage, no refresh functionality
class UserListWidget extends StatefulWidget {
  final ValueNotifier<bool> isLoading;
  final ValueNotifier<List<UserModel>> users;
  final ValueNotifier<String?> errorMessage;
  final VoidCallback? onRetry;
  final Function(UserModel)? onResetPassword;
  final int itemsPerPage;

  const UserListWidget({
    super.key,
    required this.isLoading,
    required this.users,
    required this.errorMessage,
    this.onRetry,
    this.onResetPassword,
    this.itemsPerPage = 10,
  });

  @override
  State<UserListWidget> createState() => _UserListWidgetState();
}

class _UserListWidgetState extends State<UserListWidget> {
  final PageController _pageController = PageController();
  final ValueNotifier<int> _currentPage = ValueNotifier(0);
  final ValueNotifier<int> _totalPages = ValueNotifier(0);

  @override
  void initState() {
    super.initState();
    widget.users.addListener(_updatePagination);
    _updatePagination();
  }

  @override
  void dispose() {
    widget.users.removeListener(_updatePagination);
    _pageController.dispose();
    _currentPage.dispose();
    _totalPages.dispose();
    super.dispose();
  }

  /// Update pagination when user list changes
  void _updatePagination() {
    final userCount = widget.users.value.length;
    final pages = userCount > 0 ? (userCount / widget.itemsPerPage).ceil() : 0;
    _totalPages.value = pages;

    // Reset to first page if current page is out of bounds
    if (_currentPage.value >= pages && pages > 0) {
      _currentPage.value = 0;
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.isLoading,
      builder: (context, isLoadingValue, child) {
        if (isLoadingValue) {
          return _buildLoadingState();
        }

        return ValueListenableBuilder<String?>(
          valueListenable: widget.errorMessage,
          builder: (context, errorValue, child) {
            if (errorValue != null) {
              return _buildErrorState(errorValue);
            }

            return ValueListenableBuilder<List<UserModel>>(
              valueListenable: widget.users,
              builder: (context, usersValue, child) {
                if (usersValue.isEmpty) {
                  return _buildEmptyState();
                }

                return _buildPaginatedUsersList(usersValue);
              },
            );
          },
        );
      },
    );
  }

  /// Build loading state widget with proper styling
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.buttonColor,
            strokeWidth: 3.0,
          ),
          SizedBox(height: 16),
          Text(
            'Loading users...',
            style: TextStyle(
              color: AppColors.greyText,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state widget with proper AppColors
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.red.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'Error: $error',
            style: const TextStyle(
              color: AppColors.red,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: widget.onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonColor,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state widget with proper AppColors
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: AppColors.greyText.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          const Text(
            'No users found',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.greyText,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add some users to get started',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.greyText.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Build paginated users list with navigation controls
  Widget _buildPaginatedUsersList(List<UserModel> usersList) {
    return Column(
      children: [
        // Users list
        Expanded(
          child: ValueListenableBuilder<int>(
            valueListenable: _currentPage,
            builder: (context, currentPage, child) {
              final startIndex = currentPage * widget.itemsPerPage;
              final endIndex = (startIndex + widget.itemsPerPage).clamp(0, usersList.length);
              final pageUsers = usersList.sublist(startIndex, endIndex);

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: pageUsers.length,
                itemBuilder: (context, index) {
                  final user = pageUsers[index];
                  return UserListItem(
                    user: user,
                    onResetPassword: widget.onResetPassword != null ? () => widget.onResetPassword!(user) : null,
                  );
                },
              );
            },
          ),
        ),

        // Pagination controls
        _buildPaginationControls(),
      ],
    );
  }

  /// Build pagination controls with proper styling
  Widget _buildPaginationControls() {
    return ValueListenableBuilder<int>(
      valueListenable: _totalPages,
      builder: (context, totalPages, child) {
        if (totalPages <= 1) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: const BoxDecoration(
            color: AppColors.lightContainer,
            border: Border(
              top: BorderSide(
                color: AppColors.lightDivider,
                width: 1,
              ),
            ),
          ),
          child: ValueListenableBuilder<int>(
            valueListenable: _currentPage,
            builder: (context, currentPage, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Previous button
                  _buildNavigationButton(
                    icon: Icons.chevron_left,
                    label: 'Previous',
                    onPressed: currentPage > 0 ? () => _navigateToPage(currentPage - 1) : null,
                  ),

                  // Page indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.buttonColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Page ${currentPage + 1} of $totalPages',
                      style: const TextStyle(
                        color: AppColors.buttonColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),

                  // Next button
                  _buildNavigationButton(
                    icon: Icons.chevron_right,
                    label: 'Next',
                    onPressed: currentPage < totalPages - 1 ? () => _navigateToPage(currentPage + 1) : null,
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  /// Build navigation button for pagination
  Widget _buildNavigationButton({
    required IconData icon,
    required String label,
    VoidCallback? onPressed,
  }) {
    final isEnabled = onPressed != null;

    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: 18,
        color: isEnabled ? AppColors.buttonColor : AppColors.greyText,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isEnabled ? AppColors.buttonColor : AppColors.greyText,
          fontWeight: FontWeight.w500,
        ),
      ),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Navigate to specific page
  void _navigateToPage(int page) {
    if (page >= 0 && page < _totalPages.value) {
      _currentPage.value = page;
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          page,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }
}
