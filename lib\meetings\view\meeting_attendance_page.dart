import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:society_management/injector/injector.dart';
import 'package:society_management/meetings/model/meeting_model.dart';
import 'package:society_management/meetings/repository/i_meeting_repository.dart';
import 'package:society_management/utility/utility.dart';
import 'package:society_management/widget/common_app_bar.dart';
import 'package:society_management/widget/common_gradient_button.dart';
import 'package:society_management/widget/theme_aware_card.dart';

class MeetingAttendancePage extends StatefulWidget {
  final MeetingModel meeting;

  const MeetingAttendancePage({
    super.key,
    required this.meeting,
  });

  @override
  State<MeetingAttendancePage> createState() => _MeetingAttendancePageState();
}

class _MeetingAttendancePageState extends State<MeetingAttendancePage> {
  final IMeetingRepository _meetingRepository = getIt<IMeetingRepository>();

  bool _isLoading = false;
  List<AttendeeInfo> _attendees = [];
  final Map<String, bool> _attendanceStatus = {}; // userId -> isPresent

  @override
  void initState() {
    super.initState();
    _loadAttendees();
  }

  void _loadAttendees() {
    setState(() {
      _attendees = List.from(widget.meeting.attendees);

      // Initialize attendance status based on current status
      for (var attendee in _attendees) {
        _attendanceStatus[attendee.userId] = attendee.status == AttendeeStatus.attended;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: 'Meeting Attendance',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Meeting Info Card
            _buildMeetingInfoCard(),
            const SizedBox(height: 24),

            // Attendance Summary
            _buildAttendanceSummaryCard(),
            const SizedBox(height: 24),

            // Attendee List with Checkboxes
            _buildAttendeeListCard(),
            const SizedBox(height: 32),

            // Save Attendance Button
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildMeetingInfoCard() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getMeetingTypeColor(widget.meeting.type).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getMeetingTypeIcon(widget.meeting.type),
                    color: _getMeetingTypeColor(widget.meeting.type),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.meeting.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatMeetingType(widget.meeting.type),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: _getMeetingTypeColor(widget.meeting.type),
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
                Icons.calendar_today, 'Date', DateFormat('EEEE, MMM dd, yyyy').format(widget.meeting.scheduledDate)),
            _buildInfoRow(Icons.access_time, 'Time',
                '${DateFormat('hh:mm a').format(widget.meeting.startTime)} - ${DateFormat('hh:mm a').format(widget.meeting.endTime)}'),
            _buildInfoRow(Icons.location_on, 'Venue', widget.meeting.venue),
            _buildInfoRow(Icons.person, 'Organizer', widget.meeting.organizerName),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceSummaryCard() {
    final totalInvited = _attendees.length;
    final totalPresent = _attendanceStatus.values.where((isPresent) => isPresent).length;
    final totalAbsent = totalInvited - totalPresent;
    final attendancePercentage = totalInvited > 0 ? (totalPresent / totalInvited * 100).round() : 0;

    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attendance Summary',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // Attendance Percentage
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _getAttendanceColor(attendancePercentage),
                    _getAttendanceColor(attendancePercentage).withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.analytics,
                    color: Colors.white,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '$attendancePercentage% Attendance',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '$totalPresent out of $totalInvited people',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Statistics Row
            Row(
              children: [
                Expanded(
                  child: _buildStatCard('Present', totalPresent, Colors.green, Icons.check_circle),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard('Absent', totalAbsent, Colors.red, Icons.cancel),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard('Total', totalInvited, Colors.blue, Icons.people),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, int count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendeeListCard() {
    return ThemeAwareCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Mark Attendance',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _markAllPresent,
                  icon: const Icon(Icons.check_box, size: 16),
                  label: const Text('All Present'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: _markAllAbsent,
                  icon: const Icon(Icons.check_box_outline_blank, size: 16),
                  label: const Text('All Absent'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_attendees.isNotEmpty) ...[
              ...(_attendees.asMap().entries.map((entry) {
                final index = entry.key;
                final attendee = entry.value;
                return _buildAttendeeCheckboxItem(attendee, index);
              })),
            ] else ...[
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(
                        Icons.people_outline,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No attendees found',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAttendeeCheckboxItem(AttendeeInfo attendee, int index) {
    final isPresent = _attendanceStatus[attendee.userId] ?? false;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isPresent ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isPresent ? Colors.green : Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Checkbox(
              value: isPresent,
              onChanged: (value) {
                setState(() {
                  _attendanceStatus[attendee.userId] = value ?? false;
                });
              },
              activeColor: Colors.green,
            ),
            const SizedBox(width: 12),
            CircleAvatar(
              radius: 20,
              backgroundColor: isPresent ? Colors.green.withOpacity(0.2) : Colors.grey.withOpacity(0.2),
              child: Text(
                attendee.userName.isNotEmpty ? attendee.userName[0].toUpperCase() : 'U',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isPresent ? Colors.green : Colors.grey,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    attendee.userName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isPresent ? Colors.green[700] : null,
                        ),
                  ),
                  Text(
                    '${attendee.role}${attendee.lineNumber != null ? ' - Line ${attendee.lineNumber}' : ''}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isPresent ? Colors.green : Colors.grey,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                isPresent ? 'Present' : 'Absent',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _markAllPresent() {
    setState(() {
      for (var attendee in _attendees) {
        _attendanceStatus[attendee.userId] = true;
      }
    });
  }

  void _markAllAbsent() {
    setState(() {
      for (var attendee in _attendees) {
        _attendanceStatus[attendee.userId] = false;
      }
    });
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: CommonGradientButton(
        text: _isLoading ? 'Saving Attendance...' : 'Save Attendance',
        onPressed: _isLoading ? null : _saveAttendance,
      ),
    );
  }

  Future<void> _saveAttendance() async {
    setState(() => _isLoading = true);

    try {
      // Update attendance status for each attendee
      final updatedAttendees = _attendees.map((attendee) {
        final isPresent = _attendanceStatus[attendee.userId] ?? false;
        return attendee.copyWith(
          status: isPresent ? AttendeeStatus.attended : AttendeeStatus.absent,
        );
      }).toList();

      // Update the meeting with new attendance data
      final updatedMeeting = widget.meeting.copyWith(
        attendees: updatedAttendees,
        status: MeetingStatus.completed,
        updatedAt: DateTime.now(),
      );

      // Save to repository
      final result = await _meetingRepository.updateMeeting(updatedMeeting);

      result.fold(
        (failure) => throw Exception('Failed to save attendance: ${failure.message}'),
        (_) {
          Utility.toast(message: 'Attendance saved successfully!');
          Navigator.pop(context, true);
        },
      );
    } catch (e) {
      Utility.toast(message: 'Error saving attendance: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Color _getAttendanceColor(int percentage) {
    if (percentage >= 80) return Colors.green;
    if (percentage >= 60) return Colors.orange;
    return Colors.red;
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for colors and formatting
  Color _getMeetingTypeColor(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return const Color(0xFF3B82F6);
      case MeetingType.committee:
        return const Color(0xFF10B981);
      case MeetingType.emergency:
        return const Color(0xFFEF4444);
      case MeetingType.lineMeeting:
        return const Color(0xFF8B5CF6);
      case MeetingType.maintenance:
        return const Color(0xFFF59E0B);
      case MeetingType.festivalEvent:
        return const Color(0xFFEC4899);
    }
  }

  IconData _getMeetingTypeIcon(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return Icons.groups;
      case MeetingType.committee:
        return Icons.business;
      case MeetingType.emergency:
        return Icons.warning;
      case MeetingType.lineMeeting:
        return Icons.home_work;
      case MeetingType.maintenance:
        return Icons.build;
      case MeetingType.festivalEvent:
        return Icons.celebration;
    }
  }

  String _formatMeetingType(MeetingType type) {
    switch (type) {
      case MeetingType.generalBody:
        return 'General Body Meeting';
      case MeetingType.committee:
        return 'Committee Meeting';
      case MeetingType.emergency:
        return 'Emergency Meeting';
      case MeetingType.lineMeeting:
        return 'Line Meeting';
      case MeetingType.maintenance:
        return 'Maintenance Meeting';
      case MeetingType.festivalEvent:
        return 'Festival/Event Planning';
    }
  }
}
