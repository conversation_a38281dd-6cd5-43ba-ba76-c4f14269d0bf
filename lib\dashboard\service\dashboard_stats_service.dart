import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Improved dashboard statistics service with simplified single collection structure
/// Features: Single collection, efficient updates, proper error handling, easy management
///
/// Collection Structure:
/// dashboard_stats/
/// ├── admin_global (document)           ← Global admin dashboard
/// ├── line_FIRST_LINE (document)        ← Line-specific dashboards
/// ├── line_SECOND_LINE (document)       ← Line-specific dashboards
/// ├── user_{userId} (document)          ← User-specific dashboards
/// └── ...
class DashboardStatsService {
  static const String _collectionName = 'dashboard_stats';
  static const String _adminDocId = 'admin_global';

  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get collection reference
  static CollectionReference get _collection => _firestore.collection(_collectionName);

  // ==================== ADMIN DASHBOARD METHODS ====================

  /// Update global admin dashboard statistics
  /// This method handles all admin-level statistics updates efficiently
  static Future<void> updateAdminDashboard({
    double? maintenanceCollected,
    double? maintenancePending,
    double? totalExpenses,
    int? fullyPaidUsers,
    int? partiallyPaidUsers,
    int? pendingUsers,
    int? activeMaintenance,
    int? totalMembers,
    int? totalLines,
    bool isIncrement = true,
  }) async {
    try {
      final doc = _collection.doc(_adminDocId);
      final Map<String, dynamic> updateData = {};

      // Add fields to update with proper increment/decrement or direct set
      if (maintenanceCollected != null) {
        updateData['maintenance_collected'] =
            isIncrement ? FieldValue.increment(maintenanceCollected) : maintenanceCollected;
      }

      if (maintenancePending != null) {
        updateData['maintenance_pending'] = isIncrement ? FieldValue.increment(maintenancePending) : maintenancePending;
      }

      if (totalExpenses != null) {
        updateData['total_expenses'] = isIncrement ? FieldValue.increment(totalExpenses) : totalExpenses;
      }

      if (fullyPaidUsers != null) {
        updateData['fully_paid_users'] = isIncrement ? FieldValue.increment(fullyPaidUsers) : fullyPaidUsers;
      }

      if (partiallyPaidUsers != null) {
        updateData['partially_paid_users'] =
            isIncrement ? FieldValue.increment(partiallyPaidUsers) : partiallyPaidUsers;
      }

      if (pendingUsers != null) {
        updateData['pending_users'] = isIncrement ? FieldValue.increment(pendingUsers) : pendingUsers;
      }

      if (activeMaintenance != null) {
        updateData['active_maintenance'] = isIncrement ? FieldValue.increment(activeMaintenance) : activeMaintenance;
      }

      if (totalMembers != null) {
        updateData['total_members'] = isIncrement ? FieldValue.increment(totalMembers) : totalMembers;
      }

      if (totalLines != null) {
        updateData['total_lines'] = isIncrement ? FieldValue.increment(totalLines) : totalLines;
      }

      // Always update timestamp and metadata
      updateData['updated_at'] = FieldValue.serverTimestamp();
      updateData['last_updated_by'] = 'system';
      updateData['dashboard_type'] = 'admin_global';

      await doc.set(updateData, SetOptions(merge: true));

      debugPrint('✅ Admin dashboard updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating admin dashboard: $e');
      rethrow;
    }
  }

  /// Get admin dashboard statistics
  static Future<Map<String, dynamic>?> getAdminDashboard() async {
    try {
      final doc = await _collection.doc(_adminDocId).get();
      return doc.exists ? doc.data() as Map<String, dynamic>? : null;
    } catch (e) {
      debugPrint('❌ Error getting admin dashboard: $e');
      return null;
    }
  }

  // ==================== LINE HEAD DASHBOARD METHODS ====================

  /// Update line head dashboard statistics
  /// This method handles line-specific statistics updates
  static Future<void> updateLineDashboard({
    required String lineNumber,
    double? maintenanceCollected,
    double? maintenancePending,
    double? totalExpenses,
    int? fullyPaidUsers,
    int? partiallyPaidUsers,
    int? pendingUsers,
    int? activeMaintenance,
    int? totalMembers,
    bool isIncrement = true,
  }) async {
    try {
      final docId = 'line_$lineNumber';
      final doc = _collection.doc(docId);
      final Map<String, dynamic> updateData = {};

      // Add fields to update with proper increment/decrement or direct set
      if (maintenanceCollected != null) {
        updateData['maintenance_collected'] =
            isIncrement ? FieldValue.increment(maintenanceCollected) : maintenanceCollected;
      }

      if (maintenancePending != null) {
        updateData['maintenance_pending'] = isIncrement ? FieldValue.increment(maintenancePending) : maintenancePending;
      }

      if (totalExpenses != null) {
        updateData['total_expenses'] = isIncrement ? FieldValue.increment(totalExpenses) : totalExpenses;
      }

      if (fullyPaidUsers != null) {
        updateData['fully_paid_users'] = isIncrement ? FieldValue.increment(fullyPaidUsers) : fullyPaidUsers;
      }

      if (partiallyPaidUsers != null) {
        updateData['partially_paid_users'] =
            isIncrement ? FieldValue.increment(partiallyPaidUsers) : partiallyPaidUsers;
      }

      if (pendingUsers != null) {
        updateData['pending_users'] = isIncrement ? FieldValue.increment(pendingUsers) : pendingUsers;
      }

      if (activeMaintenance != null) {
        updateData['active_maintenance'] = isIncrement ? FieldValue.increment(activeMaintenance) : activeMaintenance;
      }

      if (totalMembers != null) {
        updateData['total_members'] = isIncrement ? FieldValue.increment(totalMembers) : totalMembers;
      }

      // Always update timestamp and metadata
      updateData['line_number'] = lineNumber;
      updateData['updated_at'] = FieldValue.serverTimestamp();
      updateData['last_updated_by'] = 'system';
      updateData['dashboard_type'] = 'line_head';

      await doc.set(updateData, SetOptions(merge: true));

      debugPrint('✅ Line dashboard updated successfully for $lineNumber');
    } catch (e) {
      debugPrint('❌ Error updating line dashboard: $e');
      rethrow;
    }
  }

  /// Get line head dashboard statistics
  static Future<Map<String, dynamic>?> getLineDashboard(String lineNumber) async {
    try {
      final docId = 'line_$lineNumber';
      final doc = await _collection.doc(docId).get();
      return doc.exists ? doc.data() as Map<String, dynamic>? : null;
    } catch (e) {
      debugPrint('❌ Error getting line dashboard: $e');
      return null;
    }
  }

  // ==================== USER DASHBOARD METHODS ====================

  /// Update specific user dashboard statistics
  /// This method handles user-specific statistics updates
  static Future<void> updateUserDashboard({
    required String userId,
    required String lineNumber,
    double? maintenanceCollected,
    double? maintenancePending,
    double? totalExpenses,
    int? fullyPaidStatus, // 1 for fully paid, 0 for not paid
    int? activeMaintenance,
    bool isIncrement = true,
  }) async {
    try {
      final docId = 'user_$userId';
      final doc = _collection.doc(docId);
      final Map<String, dynamic> updateData = {};

      // Add fields to update with proper increment/decrement or direct set
      if (maintenanceCollected != null) {
        updateData['maintenance_collected'] =
            isIncrement ? FieldValue.increment(maintenanceCollected) : maintenanceCollected;
      }

      if (maintenancePending != null) {
        updateData['maintenance_pending'] = isIncrement ? FieldValue.increment(maintenancePending) : maintenancePending;
      }

      if (totalExpenses != null) {
        updateData['total_expenses'] = isIncrement ? FieldValue.increment(totalExpenses) : totalExpenses;
      }

      if (fullyPaidStatus != null) {
        updateData['fully_paid_status'] = fullyPaidStatus; // Always direct set for status
      }

      if (activeMaintenance != null) {
        updateData['active_maintenance'] = isIncrement ? FieldValue.increment(activeMaintenance) : activeMaintenance;
      }

      // Always update metadata
      updateData['user_id'] = userId;
      updateData['line_number'] = lineNumber;
      updateData['total_members'] = 1; // Always 1 for user dashboard
      updateData['updated_at'] = FieldValue.serverTimestamp();
      updateData['last_updated_by'] = 'system';
      updateData['dashboard_type'] = 'user_specific';

      await doc.set(updateData, SetOptions(merge: true));

      debugPrint('✅ User dashboard updated successfully for $userId');
    } catch (e) {
      debugPrint('❌ Error updating user dashboard: $e');
      rethrow;
    }
  }

  /// Get user dashboard statistics
  static Future<Map<String, dynamic>?> getUserDashboard(String userId) async {
    try {
      final docId = 'user_$userId';
      final doc = await _collection.doc(docId).get();
      return doc.exists ? doc.data() as Map<String, dynamic>? : null;
    } catch (e) {
      debugPrint('❌ Error getting user dashboard: $e');
      return null;
    }
  }

  // ==================== UTILITY METHODS ====================

  /// Get all line dashboards
  static Future<List<Map<String, dynamic>>> getAllLineDashboards() async {
    try {
      final querySnapshot = await _collection.where('dashboard_type', isEqualTo: 'line_head').get();

      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              })
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting all line dashboards: $e');
      return [];
    }
  }

  /// Get all user dashboards for a specific line
  static Future<List<Map<String, dynamic>>> getUserDashboardsByLine(String lineNumber) async {
    try {
      final querySnapshot = await _collection
          .where('dashboard_type', isEqualTo: 'user_specific')
          .where('line_number', isEqualTo: lineNumber)
          .get();

      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              })
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting user dashboards by line: $e');
      return [];
    }
  }

  /// Delete a dashboard document
  static Future<void> deleteDashboard(String documentId) async {
    try {
      await _collection.doc(documentId).delete();
      debugPrint('✅ Dashboard deleted successfully: $documentId');
    } catch (e) {
      debugPrint('❌ Error deleting dashboard: $e');
      rethrow;
    }
  }

  /// Batch update multiple dashboards
  static Future<void> batchUpdateDashboards(List<Map<String, dynamic>> updates) async {
    try {
      final batch = _firestore.batch();

      for (final update in updates) {
        final docId = update['doc_id'] as String;
        final data = Map<String, dynamic>.from(update);
        data.remove('doc_id');
        data['updated_at'] = FieldValue.serverTimestamp();

        batch.update(_collection.doc(docId), data);
      }

      await batch.commit();
      debugPrint('✅ Batch update completed for ${updates.length} dashboards');
    } catch (e) {
      debugPrint('❌ Error in batch update: $e');
      rethrow;
    }
  }

  // ==================== CONVENIENCE METHODS ====================

  /// Add expense to all relevant dashboards (admin, line, user)
  static Future<void> addExpenseToAllDashboards({
    required double amount,
    String? lineNumber,
    String? userId,
  }) async {
    try {
      // Update admin dashboard
      await updateAdminDashboard(totalExpenses: amount, isIncrement: true);

      // Update line dashboard if specified
      if (lineNumber != null) {
        await updateLineDashboard(
          lineNumber: lineNumber,
          totalExpenses: amount,
          isIncrement: true,
        );
      }

      // Update user dashboard if specified
      if (userId != null && lineNumber != null) {
        await updateUserDashboard(
          userId: userId,
          lineNumber: lineNumber,
          totalExpenses: amount,
          isIncrement: true,
        );
      }

      debugPrint('✅ Expense added to all relevant dashboards: ₹$amount');
    } catch (e) {
      debugPrint('❌ Error adding expense to dashboards: $e');
      rethrow;
    }
  }

  /// Remove expense from all relevant dashboards
  static Future<void> removeExpenseFromAllDashboards({
    required double amount,
    String? lineNumber,
    String? userId,
  }) async {
    try {
      // Update admin dashboard
      await updateAdminDashboard(totalExpenses: -amount, isIncrement: true);

      // Update line dashboard if specified
      if (lineNumber != null) {
        await updateLineDashboard(
          lineNumber: lineNumber,
          totalExpenses: -amount,
          isIncrement: true,
        );
      }

      // Update user dashboard if specified
      if (userId != null && lineNumber != null) {
        await updateUserDashboard(
          userId: userId,
          lineNumber: lineNumber,
          totalExpenses: -amount,
          isIncrement: true,
        );
      }

      debugPrint('✅ Expense removed from all relevant dashboards: ₹$amount');
    } catch (e) {
      debugPrint('❌ Error removing expense from dashboards: $e');
      rethrow;
    }
  }

  /// Initialize dashboard for new user
  static Future<void> initializeUserDashboard({
    required String userId,
    required String lineNumber,
  }) async {
    try {
      await updateUserDashboard(
        userId: userId,
        lineNumber: lineNumber,
        maintenanceCollected: 0.0,
        maintenancePending: 0.0,
        totalExpenses: 0.0,
        fullyPaidStatus: 0,
        activeMaintenance: 0,
        isIncrement: false, // Direct set for initialization
      );

      // Update line dashboard member count
      await updateLineDashboard(
        lineNumber: lineNumber,
        totalMembers: 1,
        isIncrement: true,
      );

      // Update admin dashboard member count
      await updateAdminDashboard(
        totalMembers: 1,
        isIncrement: true,
      );

      debugPrint('✅ User dashboard initialized for $userId');
    } catch (e) {
      debugPrint('❌ Error initializing user dashboard: $e');
      rethrow;
    }
  }
}
